<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>

<style>
/* Custom dropdown arrow styling */
.select-custom {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.select-custom:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232BA990' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* Template selection styling */
.template-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
    border-color: #2BA990;
    box-shadow: 0 4px 12px rgba(43, 169, 144, 0.2);
}

.template-card.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #2BA990;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

/* Ensure selected button styling takes precedence */
.template-choose-btn.bg-brand-main {
    background-color: #2BA990 !important;
    border-color: #2BA990 !important;
    color: white !important;
}

.template-choose-btn.bg-brand-main:hover {
    background-color: #2BA990 !important;
    opacity: 0.9;
}
</style>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      <?php include get_stylesheet_directory() . '/customer/breadcrumb-create-website.php'; ?>

      <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-4">
          
            <!-- Danh sách website sẽ render ở đây -->
            <!-- <div class="text-neutral-medium text-center py-12">No websites found.</div> -->
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
               <!-- Cột trái: 4/12 -->
               <div class="xl:col-span-4 col-span-12 relative overflow-hidden min-h-[220px] flex flex-col justify-start">
                  <div class="font-semibold text-lg">Your Website</div>
                  <form class="flex flex-col gap-8 mt-2" id="formCreateWebsite">
                     <!-- Business Name Input -->
                     <div class="flex flex-col gap-2">
                        <label for="business_name" class="text-primary-main text-base font-medium">
                           Company Name
                        </label>
                        <input type="text" 
                           id="business_name" 
                           name="business_name" 
                           placeholder="Company Name"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     <!-- Business Name Input -->
                     <div class="flex flex-col gap-2">
                        <label for="business_phone" class="text-primary-main text-base font-medium">
                           Phone
                        </label>
                        <input type="text" 
                           id="business_phone" 
                           name="business_phone" 
                           placeholder="Phone"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     <!-- Business Description Input -->
                     <div class="flex flex-col gap-2">
                        <label for="business_address" class="text-primary-main text-base font-medium">
                           Address
                        </label>
                        <input type="text" 
                           id="business_address" 
                           name="business_address" 
                           placeholder="Address"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     
                  </form>
               </div>

               <!-- Cột phải: 8/12 -->
               <div class="xl:col-span-8 col-span-12 relative rounded-2xl pb-10  bg-white min-h-[220px] flex flex-col justify-start">
                  <div class="font-semibold text-lg">Choose Template</div>
                  <div class="flex justify-left mt-2">
                     <form id="search-form" class="w-full flex flex-col gap-4 text-base pl-0 p-[20px]" id="search-form">
                        <!-- Search Row -->
                        <div class="flex flex-col md:flex-row gap-4 items-center w-full xl:">
                           <div class="flex items-center text-neutral-light font-normal w-full my-auto rounded-full">
                              <div class="!bg-white border border-[#E6E8EC] h-[48px] flex w-full items-center gap-[8px] px-4 rounded-full relative">
                                 <img
                                 src="https://cdn.builder.io/api/v1/image/assets/4f22a764650b4745a83d9de62f28fa3f/122d4a6b1e4e8fb08f0091ab133ed7e594cd60fd"
                                 alt="Search"
                                 class="aspect-[1] object-contain w-6 shrink-0 my-auto"
                                 />
                                 <input
                                 type="text"
                                 placeholder="Search..."
                                 class="flex-1 !bg-transparent !shadow-none !border-none !outline-none text-neutral-light placeholder:text-neutral-light w-full pr-8"
                                 id="search-template"
                                 value="<?php if(isset($_GET['keyword'])) echo $_GET['keyword']; ?>"
                                 />
                                 <!-- Icon clear- hidden by default -->
                                 <button type="button" id="clearButton" class="absolute right-2 top-1/2 -translate-y-1/2 bg-brand-color-01 p-4 rounded-full w-[18px] h-[18px] flex items-center justify-center hover:bg-brand-color-01 hidden">
                                    <i class="fa-solid fa-close text-neutral-medium text-xs"></i>
                                 </button>
                              </div>
                           </div>
                           <button
                           type="submit" id="search-template-button"
                           class="flex h-[48px] items-center justify-center text-black font-semibold w-full md:w-[196px] my-auto rounded-full !bg-brand-main hover:bg-brand-main active:!bg-brand-main px-5 py-3 hover:opacity-90 transition-opacity"
                           >
                              Search
                           </button>
                        </div>

                        <!-- Filter Row -->
                        <div class="flex flex-col md:flex-row gap-4 items-start md:items-center w-full">
                           <label class="text-primary-main text-base font-medium whitespace-nowrap">Filter</label>
                           <!-- Industry Dropdown -->
                           <div class="flex flex-col gap-2 w-full md:w-auto">
                              <!-- Industry Dropdown Container -->
                              <div class="relative z-30">
                                 <select id="industry"
                                          class="w-full md:min-w-[220px] !pl-[16px] !pr-[40px] !py-[12px] !rounded-full border border-[#E6E9F5]
                                             appearance-none bg-white
                                             focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                             cursor-pointer
                                             transition-all duration-200 !h-[48px] text-16 select-custom">
                                       <option value="" disabled selected>Select a filter</option>

                                 </select>

                                 <!-- Custom Dropdown Content -->
                                 <div id="industryDropdown"
                                       class="dropdown-content absolute left-0 right-0 top-full mt-1 hidden
                                             bg-white !rounded-full border border-[#E6E9F5] shadow-lg
                                             max-h-[300px] overflow-y-auto
                                             overflow-hidden z-40">
                                       <!-- Dropdown Items -->
                                       <div class="py-2 bg-white">

                                       </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </form>
                  </div>
                  <script>
                    jQuery(document).ready(function($) {
                        let debounceTimeout;
                        function fetchTemplates(keyword = "", industry_selected = "") {
                           // Nếu có từ khóa thì tạo filters là mảng, không thì để null
                           const filters = ["status_id:=(2)","is_kit:=(0)"];
                           const search = keyword ? keyword : "<?php if(isset($_GET['keyword'])) echo $_GET['keyword']; ?>";

                           const query = `
                           query Template_list($filters: [String!], $search: String!) {
                              template_list(body: {filters: $filters, search: $search }) {
                                    totalCount
                                    totalPages
                                    currentPage
                                    data {
                                       id
                                       name
                                       desc
                                       image_id
                                       image {
                                          id
                                          file_name
                                          file_url
                                       }
                                       industries {
                                          id
                                          name
                                       }
                                       info
                                    }
                              }
                           }`;

                           $.ajax({
                              url: iptHomeAjax.ajax_url,
                              type: 'POST',
                              dataType: 'json',
                              data: {
                                    action: 'ipt_home_graphql',
                                    query: query,
                                    variables: JSON.stringify({ filters: filters, search: search })
                              },
                              success: function(response) {
                                    if(response.data && response.data.template_list) {
                                       renderTemplates(response.data.template_list.data, industry_selected);
                                    }
                              }
                           });
                        }

                        // Get all templates
                        fetchTemplates("");

                        $('#search-template-button').on('click', function(e) {
                           e.preventDefault();
                           const keyword = $('#search-template').val();
                           fetchTemplates(keyword);
                        });

                        $('#search-form').on('submit', function(e) {
                           e.preventDefault();
                           const keyword = $('#search-template').val();
                           fetchTemplates(keyword);
                        });

                        // Render templates
                        function renderTemplates(list, industry_selected = "") {
                           let html = '';

                           // Check if no templates found after filtering
                           if(list.length === 0) {
                              html = '<div class="col-span-full text-neutral-medium text-center py-12">No templates found!</div>';
                              $('#template-list').html(html);

                              // Trigger content loaded event
                              $(document).trigger('contentLoaded');

                              $('#continueBtn').hide();
                              return;
                           } else {
                              // $('#continueBtn').show();
                           }

                           list.forEach(function(item) {
                              let industry_name = item.industries[0] ? item.industries[0].name : "";
                              if(industry_selected.length > 0) {
                                    if(industry_selected !== industry_name) {
                                       return;
                                    }
                              }
                              
                              html += `
                                    <article class="template-card flex relative flex-col border bg-white rounded-[8px] overflow-hidden border-solid border-fifth-main h-full" data-template-id="${item.id}">
                                       <div class="absolute top-2 right-2 z-[999] cursor-pointer favorite-toggle">
                                          <svg class="heart-outline" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                             <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9929 1.71691C8.65298 -0.327979 5.19252 -0.746436 2.50521 1.54965C-0.401577 4.03328 -0.823907 8.21609 1.47471 11.1739C2.34724 12.2967 4.05023 13.9835 5.68684 15.5282C7.34261 17.0911 8.99457 18.5679 9.80923 19.2894L9.82474 19.3031C9.90133 19.371 9.99669 19.4555 10.0881 19.5244C10.1975 19.6068 10.3547 19.7091 10.5645 19.7717C10.8434 19.8549 11.1432 19.8549 11.4221 19.7717C11.6319 19.7091 11.789 19.6068 11.8984 19.5244C11.9898 19.4555 12.0852 19.371 12.1618 19.3031L12.1773 19.2894C12.992 18.5679 14.6439 17.0911 16.2997 15.5282C17.9363 13.9835 19.6393 12.2967 20.5118 11.1739C22.8016 8.22745 22.4445 4.01238 19.4709 1.54088C16.7537 -0.717541 13.3301 -0.328758 10.9929 1.71691ZM10.2333 3.78575C8.52828 1.79238 5.818 1.34975 3.80441 3.07021C1.7011 4.86733 1.41807 7.84171 3.05392 9.9467C3.81701 10.9286 5.40681 12.5137 7.05966 14.0738C8.6027 15.5303 10.1462 16.9145 10.9933 17.6663C11.8403 16.9145 13.3838 15.5303 14.9269 14.0738C16.5797 12.5137 18.1695 10.9286 18.9326 9.9467C20.5773 7.83034 20.3152 4.84319 18.1925 3.07898C16.1256 1.36105 13.4507 1.80119 11.7532 3.78575C11.5632 4.00786 11.2856 4.13573 10.9933 4.13573C10.701 4.13573 10.4233 4.00786 10.2333 3.78575Z" fill="white"/>
                                          </svg>
                                          <svg class="heart-filled hidden" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                             <path fill-rule="evenodd" clip-rule="evenodd" d="M0 7.48C0 3.97111 2.65774 0 6.88889 0C8.71593 0 10.0661 0.710504 11 1.51082C11.9339 0.710503 13.2841 0 15.1111 0C19.3423 0 22 3.97111 22 7.48C22 9.32544 21.2854 11.0297 20.293 12.5091C19.2998 13.9897 17.9924 15.2999 16.7111 16.3798C15.4261 17.4629 14.1397 18.3372 13.1636 18.9411C12.6749 19.2435 12.2596 19.4807 11.9558 19.6447C11.8047 19.7263 11.6762 19.7924 11.5771 19.8404C11.5289 19.8637 11.4787 19.8871 11.4319 19.9068C11.4098 19.9161 11.3759 19.9299 11.3368 19.9431C11.3177 19.9496 11.2854 19.9601 11.2456 19.9699C11.2202 19.9762 11.1237 20 11 20C10.8763 20 10.7805 19.9763 10.7551 19.9701C10.7153 19.9602 10.6823 19.9496 10.6632 19.9431C10.6241 19.9299 10.5902 19.9161 10.5681 19.9068C10.5213 19.8871 10.4711 19.8637 10.4229 19.8404C10.3238 19.7924 10.1953 19.7263 10.0442 19.6447C9.74037 19.4807 9.32509 19.2435 8.83637 18.9411C7.86027 18.3372 6.57395 17.4629 5.28889 16.3798C4.00758 15.2999 2.70022 13.9897 1.70703 12.5091C0.714641 11.0297 0 9.32544 0 7.48Z" fill="#E54D2E"/>
                                          </svg>
                                       </div>
                                       <div class="relative w-full h-[200px]">
                                          <img src="${item.image ? item.image.file_url : 'https://cdn.builder.io/api/v1/image/assets/TEMP/11ce7b98d60f888e40ee582f5d48055fe0e8fdca'}"
                                                alt="${item.name}" class="w-full h-full object-cover" />
                                          <div class="flex h-[200px] justify-between items-center absolute px-3 py-0 top-0 inset-x-0">
                                                <div class="cursor-pointer">
                                                   <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                                      class="chevron-left">
                                                      <path
                                                            d="M10.757 12.0001L15.707 16.9501C15.8025 17.0423 15.8787 17.1526 15.9311 17.2747C15.9835 17.3967 16.0111 17.5279 16.0123 17.6607C16.0134 17.7934 15.9881 17.9251 15.9378 18.048C15.8875 18.1709 15.8133 18.2826 15.7194 18.3765C15.6255 18.4703 15.5139 18.5446 15.391 18.5949C15.2681 18.6452 15.1364 18.6705 15.0036 18.6693C14.8708 18.6682 14.7396 18.6406 14.6176 18.5882C14.4956 18.5358 14.3852 18.4596 14.293 18.3641L8.636 12.7071C8.44853 12.5195 8.34322 12.2652 8.34322 12.0001C8.34322 11.7349 8.44853 11.4806 8.636 11.2931L14.293 5.63606C14.4816 5.4539 14.7342 5.35311 14.9964 5.35538C15.2586 5.35766 15.5094 5.46283 15.6948 5.64824C15.8802 5.83365 15.9854 6.08446 15.9877 6.34666C15.99 6.60885 15.8892 6.86146 15.707 7.05006L10.757 12.0001Z"
                                                            fill="white"></path>
                                                   </svg>
                                                </div>
                                                <div class="cursor-pointer">
                                                   <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                                      class="chevron-right">
                                                      <path
                                                            d="M13.314 12.071L8.36399 7.12098C8.18184 6.93238 8.08104 6.67978 8.08332 6.41758C8.0856 6.15538 8.19077 5.90457 8.37618 5.71916C8.56158 5.53375 8.8124 5.42859 9.07459 5.42631C9.33679 5.42403 9.58939 5.52482 9.77799 5.70698L15.435 11.364C15.6225 11.5515 15.7278 11.8058 15.7278 12.071C15.7278 12.3361 15.6225 12.5905 15.435 12.778L9.77799 18.435C9.58939 18.6171 9.33679 18.7179 9.07459 18.7157C8.8124 18.7134 8.56158 18.6082 8.37618 18.4228C8.19077 18.2374 8.0856 17.9866 8.08332 17.7244C8.08104 17.4622 8.18184 17.2096 8.36399 17.021L13.314 12.071Z"
                                                            fill="white"></path>
                                                   </svg>
                                                </div>
                                          </div>
                                       </div>
                                       <div class="flex flex-col items-start p-2 gap-[4px] self-stretch flex-1">
                                          <div class="text-secondary-main text-16 font-normal">
                                                ${item.industries[0] ? item.industries[0].name : ''}
                                          </div>
                                          <h3 class="text-secondary-main text-18 font-bold">
                                                ${item.name}
                                          </h3>
                                          
                                       </div>
                                       <div class="flex items-start gap-2 p-2 self-stretch">
                                          <button  onclick="window.open('${item.info.domain ? (item.info.domain.startsWith('http') ? item.info.domain : 'https://' + item.info.domain) : ''}', '_blank')"
                                                class="border border-fourth-main flex-1 text-neutral-strong font-medium cursor-pointer bg-transparent hover:bg-transparent py-1.5 px-3 rounded-[6px] text-12 min-h-[32px]">
                                                <span class="flex items-center justify-center gap-1">
                                                   <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" fill="currentColor"/>
                                                   </svg>
                                                   Preview
                                                </span>
                                          </button>
                                          <button data-template-id="${item.id}"
                                                class="template-choose-btn border border-fourth-main flex-1 text-neutral-strong font-medium cursor-pointer bg-transparent hover:bg-transparent py-1.5 px-3 rounded-[6px] text-12 min-h-[32px]">
                                                <span class="choose-text">Choose</span>
                                                <span class="selected-text hidden">
                                                   <span class="flex items-center justify-center gap-1">
                                                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="white"/>
                                                      </svg>
                                                      Selected
                                                   </span>
                                                </span>
                                          </button>
                                       </div>
                                    </article>
                              `;
                           });
                           $('#template-list').html(html);

                           // Trigger content loaded event to reinitialize favorites
                           $(document).trigger('contentLoaded');
                        }
                        
                        /* Industry list */
                        const industry_filters = ["status_id:=(2)"];
                        const industry_sort = "name:asc";
                        const queryIndustry = `
                           query Industry_list($filters: [String!],  $sort: String!) {
                              industry_list(body: { filters: $filters, sort: $sort }) {
                                    id
                                    name
                                    status_id
                              }
                           }
                        `;

                        $.ajax({
                              url: iptHomeAjax.ajax_url,
                              type: 'POST',
                              dataType: 'json',
                              data: {
                                 action: 'ipt_home_graphql',
                                 query: queryIndustry,
                                 variables: JSON.stringify({ filters: industry_filters, sort: industry_sort })

                              },
                              success: function(response) {
                                 if(response.data && response.data.industry_list) {      
                                    renderIndustries(response.data.industry_list);
                                 } else {
                                    // No data
                                 }
                              },
                              error: function(xhr, status, error) {
                                 console.error(error);
                              }
                        });

                        // Hàm render ra HTML
                        function renderIndustries(list) {
                              let html = '<option value="">Select a filter</option>';  
                              list.forEach(function(item) {
                                 html += `
                                       <option value="${item.id}">${item.name}</option>
                                 `;      
                              });
                              $('#industry').html(html);

                              let html_dropdown = '';  
                              list.forEach(function(item) {
                                 html_dropdown += `
                                    <div data-industry-id="${item.id}" class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          ${item.name}
                                    </div>
                                 `;      
                              });
                              $('#industryDropdown').html(html_dropdown);
                        }   

                         // Xử lý sự kiện khi thay đổi giá trị dropdown
                        $('#industry').on('change', function() {
                           const selectedOption = $(this).find('option:selected');
                           const industryName = selectedOption.text();
                           fetchTemplates("", industryName);
                        });
                        
                        // Xử lý sự kiện khi click vào item trong dropdown
                        $(document).on('click', '#industryDropdown div', function() {
                           const industryName = $(this).data('industry-name');
                           const industryId = $(this).data('industry-id');
                           
                           // Cập nhật giá trị cho select
                           $('#industry').val(industryId);
                           
                           // Ẩn dropdown
                           $('#industryDropdown').addClass('hidden');
                           
                           // Gọi lại fetchTemplates với industry đã chọn
                           fetchTemplates("", industryName);
                        });
                      
                        
                     });
                  </script> 
                  <!-- List templates -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 w-full gap-[20px] mt-[16px]" id="template-list">

                  </div>
                  <!-- Navigation Buttons -->
                        <div class="flex gap-4 mt-8 justify-end">
                           <button type="button"
                                    id="continueBtn"
                                    class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 max-w-[196px] text-primary-main font-semibold
                                       transition-all duration-200 hidden"
                                    >
                              Next
                           </button>
                        </div>

                        <script>
                        jQuery(document).ready(function($) {
                            // Get current user ID
                            const userId = <?php echo get_current_user_id(); ?>;
                            const cookieName = `favorites_user_${userId}`;

                            // Function to get favorites from cookie
                            function getFavorites() {
                                const cookieValue = getCookie(cookieName);
                                return cookieValue ? JSON.parse(cookieValue) : [];
                            }

                            // Function to save favorites to cookie
                            function saveFavorites(favorites) {
                                const expiryDays = 365; // Cookie expires in 1 year
                                setCookie(cookieName, JSON.stringify(favorites), expiryDays);
                            }

                            // Function to set cookie
                            function setCookie(name, value, days) {
                                const expires = new Date();
                                expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
                                document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
                            }

                            // Function to get cookie
                            function getCookie(name) {
                                const nameEQ = name + "=";
                                const ca = document.cookie.split(';');
                                for(let i = 0; i < ca.length; i++) {
                                    let c = ca[i];
                                    while (c.charAt(0) == ' ') c = c.substring(1, c.length);
                                    if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
                                }
                                return null;
                            }

                            // Function to get template ID from element
                            function getTemplateId(element) {
                                // Try to get template ID from data attribute or closest template container
                                const templateId = element.closest('[data-template-id]').attr('data-template-id') ||
                                                 element.closest('.template-item').attr('data-id') ||
                                                 element.attr('data-template-id');
                                return templateId;
                            }

                            // Initialize favorites on page load
                            function initializeFavorites() {
                                const favorites = getFavorites();

                                $('.favorite-toggle').each(function() {
                                    const templateId = getTemplateId($(this));
                                    if (templateId && favorites.includes(templateId)) {
                                        // Set as favorited
                                        $(this).find('.heart-outline').addClass('hidden');
                                        $(this).find('.heart-filled').removeClass('hidden');
                                    } else {
                                        // Set as not favorited
                                        $(this).find('.heart-outline').removeClass('hidden');
                                        $(this).find('.heart-filled').addClass('hidden');
                                    }
                                });
                            }

                            // Handle favorite icon toggle
                            $(document).on('click', '.favorite-toggle', function(e) {
                                e.preventDefault();
                                console.log('Favorite toggle clicked');

                                const templateId = getTemplateId($(this));

                                if (!templateId) {
                                    console.error('Template ID not found');
                                    return;
                                }

                                let favorites = getFavorites();
                                const isFavorited = favorites.includes(templateId);

                                if (isFavorited) {
                                    // Remove from favorites
                                    favorites = favorites.filter(id => id !== templateId);
                                    $(this).find('.heart-outline').removeClass('hidden');
                                    $(this).find('.heart-filled').addClass('hidden');
                                    console.log(`Removed template ${templateId} from favorites`);
                                } else {
                                    // Add to favorites
                                    favorites.push(templateId);
                                    $(this).find('.heart-outline').addClass('hidden');
                                    $(this).find('.heart-filled').removeClass('hidden');
                                    console.log(`Added template ${templateId} to favorites`);
                                }

                                // Save updated favorites
                                saveFavorites(favorites);
                                console.log('Current favorites:', favorites);
                            });

                            // Initialize favorites when page loads
                            initializeFavorites();

                            // Re-initialize favorites when new content is loaded (e.g., AJAX)
                            $(document).on('contentLoaded', function() {
                                initializeFavorites();
                            });

                            // Debug function to show current favorites (can be called from console)
                            window.showFavorites = function() {
                                const favorites = getFavorites();
                                console.log('Current favorites for user <?php echo get_current_user_id(); ?>:', favorites);
                                return favorites;
                            };

                            // Debug function to clear all favorites (can be called from console)
                            window.clearFavorites = function() {
                                saveFavorites([]);
                                initializeFavorites();
                                console.log('All favorites cleared for user <?php echo get_current_user_id(); ?>');
                            };
                        });
                        </script>
                  </div>
               
            </div>
            
         </div>

         
      </main>

   </div>
</div>

<script>
function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}

// Search input clear button functionality (copied from dashboard.php)
document.addEventListener('DOMContentLoaded', function() {
   const searchInput = document.getElementById('search-template');
   const clearButton = document.getElementById('clearButton');

   if (searchInput && clearButton) {
      // Show/hide clear button based on input content
      function toggleClearButton() {
         if (searchInput.value.trim() !== '') {
            clearButton.classList.remove('hidden');
         } else {
            clearButton.classList.add('hidden');
         }
      }

      // Listen for input changes
      searchInput.addEventListener('input', toggleClearButton);
      searchInput.addEventListener('keyup', toggleClearButton);
      searchInput.addEventListener('paste', function() {
         // Delay to allow paste content to be processed
         setTimeout(toggleClearButton, 10);
      });

      // Clear button click handler
      clearButton.addEventListener('click', function() {
         searchInput.value = '';
         searchInput.focus();
         toggleClearButton();

         // Trigger search functionality if needed
         // You can add custom search reset logic here
         console.log('Template search input cleared');

         // If there's a search function, you can call it here
         // For example: performSearch('');
      });

      // Initial check on page load
      toggleClearButton();
   }

   // Next button visibility logic
   function checkFormCompletion() {
      const businessName = document.getElementById('business_name').value.trim();
      const businessPhone = document.getElementById('business_phone').value.trim();
      const businessAddress = document.getElementById('business_address').value.trim();
      const selectedTemplate = document.querySelector('.template-card.selected');
      const nextBtn = document.getElementById('continueBtn');

      console.log('Form check:', {
         businessName: businessName,
         businessPhone: businessPhone,
         businessAddress: businessAddress,
         selectedTemplate: selectedTemplate ? 'YES' : 'NO'
      });

      // Show Next button only if all form fields are filled and template is selected
      if (businessName !== '' && businessPhone !== '' && businessAddress !== '' && selectedTemplate) {
         nextBtn.classList.remove('hidden');
         console.log('✅ Next button shown');
      } else {
         nextBtn.classList.add('hidden');
         console.log('❌ Next button hidden');
      }
   }

   // Listen for form field changes
   document.getElementById('business_name').addEventListener('input', checkFormCompletion);
   document.getElementById('business_phone').addEventListener('input', checkFormCompletion);
   document.getElementById('business_address').addEventListener('input', checkFormCompletion);

   // Listen for template selection
   document.addEventListener('click', function(e) {
      // Check if clicked element is a choose button or template card
      const chooseBtn = e.target.closest('.template-choose-btn');
      const templateCard = e.target.closest('.template-card');

      if (chooseBtn || templateCard) {
         // Remove previous selection from all cards
         document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
            // Reset all buttons to "Choose" state
            const btn = card.querySelector('.template-choose-btn');
            if (btn) {
               btn.classList.remove('bg-brand-main', 'text-white', 'border-brand-main');
               btn.classList.add('border-fourth-main', 'text-neutral-strong');
               btn.querySelector('.choose-text').classList.remove('hidden');
               btn.querySelector('.selected-text').classList.add('hidden');
            }
         });

         // Add selection to clicked template
         const currentCard = chooseBtn ? chooseBtn.closest('.template-card') : templateCard;
         currentCard.classList.add('selected');

         // Update button to "Selected" state
         const currentBtn = currentCard.querySelector('.template-choose-btn');
         if (currentBtn) {
            currentBtn.classList.remove('border-fourth-main', 'text-neutral-strong');
            currentBtn.classList.add('bg-brand-main', 'text-white', 'border-brand-main');
            currentBtn.querySelector('.choose-text').classList.add('hidden');
            currentBtn.querySelector('.selected-text').classList.remove('hidden');
         }

         // Check form completion after template selection
         checkFormCompletion();
      }
   });

   // Initial check on page load
   checkFormCompletion();
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
