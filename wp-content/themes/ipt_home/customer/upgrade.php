<?php
/**
 * Customer Upgrade/Downgrade Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url());
    exit;
}

// Handle downgrade processing if requested
if (isset($_GET['process_downgrade']) && $_GET['process_downgrade'] === '1' && isset($_GET['order_id'])) {
    $order_id = intval($_GET['order_id']);
    $order = wc_get_order($order_id);

    if ($order && $order->get_user_id() === get_current_user_id()) {
        // Process downgrade
        $order->add_meta_data('_plan_cancelled', 'yes');
        $order->add_meta_data('_plan_cancelled_date', current_time('mysql'));
        $order->add_order_note('Plan cancelled by user (downgrade). Access continues until expiration.');
        $order->save();

        // Redirect back to subscription page with success message
        wp_redirect(add_query_arg([
            'customer_page' => 'subscription',
            'downgrade_success' => '1'
        ], home_url()));
        exit;
    }
}

// Include required headers
include get_stylesheet_directory() . '/customer/header-customer.php';
include get_stylesheet_directory() . '/customer/header-dashboard.php';

// Get URL parameters
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'upgrade';
$current_plan_id = isset($_GET['current_plan']) ? sanitize_text_field($_GET['current_plan']) : '';
$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
$trial_days = isset($_GET['trial_days']) ? intval($_GET['trial_days']) : 0;
$renewal_date = isset($_GET['renewal_date']) ? sanitize_text_field($_GET['renewal_date']) : '';

// Get current user
$current_user = wp_get_current_user();

// Get current order info if available
$current_order = null;
$current_product = null;
$current_subscription_type = null;

if ($order_id) {
    $current_order = wc_get_order($order_id);
    if ($current_order) {
        // Use same logic as subscription page to determine plan type
        $is_trial_order = $current_order->get_meta('_is_trial_order') === 'yes';
        $order_total = floatval($current_order->get_total());

        if ($is_trial_order && $order_total == 0) {
            // This is an actual trial order
            $current_subscription_type = '1'; // Trial
        } elseif ($order_total > 0) {
            // Use order total to determine plan type (same logic as functions.php)
            if ($order_total >= 50 && $order_total <= 60) {
                $current_subscription_type = '2'; // Basic
            } elseif ($order_total >= 90 && $order_total <= 110) {
                $current_subscription_type = '3'; // Premium
            } else {
                // Fallback to product meta
                foreach ($current_order->get_items() as $item) {
                    $current_product = $item->get_product();
                    if ($current_product) {
                        $current_subscription_type = $current_product->get_meta('subscription_type_id');
                        break;
                    }
                }
            }
        } else {
            // Fallback to product meta for $0 orders
            foreach ($current_order->get_items() as $item) {
                $current_product = $item->get_product();
                if ($current_product) {
                    $current_subscription_type = $current_product->get_meta('subscription_type_id');
                    break;
                }
            }
        }
    }
}

// Get available plans (products with subscription_type_id)
$available_plans = [];
$products = wc_get_products([
    'status' => 'publish',
    'limit' => -1,
    'meta_query' => [
        [
            'key' => 'subscription_type_id',
            'compare' => 'EXISTS'
        ]
    ]
]);

foreach ($products as $product) {
    $subscription_type_id = $product->get_meta('subscription_type_id');
    $subscription_trial_days = (int) $product->get_meta('subscription_trial_days');
    $subscription_display_order = (int) $product->get_meta('subscription_display_order');
    $subscription_plan_id = $product->get_meta('subscription_plan_id');

    $available_plans[] = [
        'id' => $product->get_id(),
        'name' => $product->get_name(),
        'price' => $product->get_price(),
        'regular_price' => $product->get_regular_price(),
        'sale_price' => $product->get_sale_price(),
        'is_on_sale' => $product->is_on_sale(),
        'price_html' => $product->get_price_html(),
        'subscription_type_id' => $subscription_type_id,
        'subscription_plan_id' => $subscription_plan_id,
        'trial_days' => $subscription_trial_days,
        'display_order' => $subscription_display_order,
        'description' => $product->get_description(),
        'short_description' => $product->get_short_description(),
    ];
}

// Sort by display order
usort($available_plans, function($a, $b) {
    return $a['display_order'] - $b['display_order'];
});

// Check if user has already used trial
$user_has_used_trial = function_exists('ipt_home_has_used_trial') ? ipt_home_has_used_trial() : false;

// Check if current plan is cancelled
$is_plan_cancelled = false;
if ($current_order) {
    $is_plan_cancelled = $current_order->get_meta('_plan_cancelled') === 'yes';
}

// Filter plans based on action
$filtered_plans = [];
if ($action === 'upgrade') {
    // For upgrade: show plans with higher subscription_type_id
    $current_type = $current_subscription_type ? intval($current_subscription_type) : 0; // Changed from 1 to 0 for users with no subscription

    // Special handling for cancelled plans - they can upgrade to any plan (except trial)
    if ($is_plan_cancelled) {
        foreach ($available_plans as $plan) {
            $plan_type = intval($plan['subscription_type_id']);

            // Skip trial plans (type 1) - cancelled users can't get trial again
            if ($plan_type === 1) {
                continue;
            }

            $filtered_plans[] = $plan;
        }
    } else {
        // Normal upgrade logic
        foreach ($available_plans as $plan) {
            $plan_type = intval($plan['subscription_type_id']);

            // Skip trial plans (type 1) if user has already used trial
            if ($plan_type === 1 && $user_has_used_trial) {
                continue;
            }

            // For users with no subscription (current_type = 0), show all available plans
            if ($current_type === 0 || $plan_type > $current_type) {
                $filtered_plans[] = $plan;
            }
        }
    }
} else {
    // For downgrade: show plans with lower subscription_type_id
    // Note: Downgrade should never show trial plans (users can't downgrade to trial)
    $current_type = $current_subscription_type ? intval($current_subscription_type) : 3;
    foreach ($available_plans as $plan) {
        $plan_type = intval($plan['subscription_type_id']);

        // Never allow downgrade to trial (type 1)
        if ($plan_type === 1) {
            continue;
        }

        if ($plan_type < $current_type) {
            $filtered_plans[] = $plan;
        }
    }
}

$page_title = $action === 'upgrade' ? 'Upgrade Plan' : 'Downgrade Plan';

// Adjust description for users with no subscription
if ($action === 'upgrade' && !$current_subscription_type) {
    $page_description = 'Choose a plan to get started with your subscription';
} else {
    $page_description = $action === 'upgrade' ?
        'Choose a higher plan to unlock more features' :
        'Choose a lower plan. Your remaining days will be preserved.';
}

?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- Main Content -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
            <!-- Header with navigation -->
            <div class="flex items-center mb-6">
               <div class="flex items-center text-sm text-gray-500">
                  <!-- <span>Settings</span> -->
                   <a href="<?php echo site_url('/customer/subscription'); ?>" class="text-gray-500 hover:text-gray-700">Settings</a>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <a href="<?php echo site_url('/customer/subscription'); ?>" class="text-gray-500 hover:text-gray-700">Subscription management</a>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <span><?php echo esc_html($page_title); ?></span>
               </div>
            </div>

            <!-- Title and action buttons -->
            <div class="flex justify-between items-center mb-8">
               <div>
                  <h1 class="text-2xl font-bold"><?php echo esc_html($page_title); ?></h1>
                  <p class="text-gray-600 mt-2"><?php echo esc_html($page_description); ?></p>
               </div>
               <div class="flex gap-2">
                  <a href="<?php echo esc_url(add_query_arg('customer_page', 'subscription', home_url())); ?>"
                     class="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50">
                     Back to Subscription
                  </a>
               </div>
            </div>
               
            <!-- Current Plan Info -->
            <?php if ($current_product): ?>
            <div class="bg-ipt-bg-1 p-4 rounded-lg mb-6">
               <div class="flex justify-between items-start">
                  <div>
                     <h3 class="font-semibold text-gray-900">Current Plan</h3>
                     <p class="text-lg font-medium text-gray-800 mt-1"><?php echo esc_html($current_product->get_name()); ?></p>
                     <?php if ($action === 'upgrade' && $trial_days > 0): ?>
                        <p class="text-sm text-orange-600 mt-1">
                           <svg class="inline w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                           </svg>
                           Trial expires in <?php echo $trial_days; ?> days
                        </p>
                     <?php elseif ($action === 'downgrade' && $renewal_date): ?>
                        <p class="text-sm text-green-600 mt-1">
                           <svg class="inline w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm0 0v4a2 2 0 002 2h4a2 2 0 002-2v-4"></path>
                           </svg>
                           Expired on <?php echo esc_html($renewal_date); ?>
                        </p>
                     <?php endif; ?>
                  </div>
                  <div class="text-right">
                     <?php if ($current_product->get_regular_price() && $current_product->is_on_sale()): ?>
                        <div class="text-sm text-gray-500 line-through mb-1">
                           <?php echo wc_price($current_product->get_regular_price()); ?>
                        </div>
                        <span class="text-2xl font-bold text-red-600">
                           <?php echo wc_price($current_product->get_sale_price()); ?>
                        </span>
                        <!-- <div class="text-xs text-red-600 font-medium mt-1">Sale Price</div> -->
                     <?php else: ?>
                        <span class="text-2xl font-bold text-gray-900">
                           <?php echo wc_price($current_product->get_price()); ?>
                        </span>
                     <?php endif; ?>
                     <p class="text-sm text-gray-500">current plan</p>
                  </div>
               </div>
            </div>
            <?php endif; ?>
            </div>

            <!-- Available Plans Section -->
            <div class="mb-6">
               <h2 class="text-lg font-semibold text-gray-900 mb-4">
                  <?php echo $action === 'upgrade' ? 'Available Upgrades' : 'Available Downgrades'; ?>
               </h2>

               <?php if (empty($filtered_plans)): ?>
               <div class="text-center py-12 bg-gray-50 rounded-lg">
                  <div class="text-gray-500">
                     <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                     </svg>
                     <h3 class="text-lg font-medium text-gray-900 mb-2">No plans available</h3>
                     <p class="text-gray-500 mb-6">
                        <?php echo $action === 'upgrade' ? 'You are already on the highest plan available.' : 'No lower plans are available for downgrade.'; ?>
                     </p>
                     <a href="<?php echo esc_url(add_query_arg('customer_page', 'subscription', home_url())); ?>"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700">
                        <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Subscription
                     </a>
                  </div>
               </div>
               <?php else: ?>
               <div class="flex justify-center">
                  <?php if (count($filtered_plans) < 4): ?>
                  <div class="flex flex-wrap justify-center gap-6 max-w-7xl w-full">
                  <?php else: ?>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-6 max-w-7xl w-full">
                  <?php endif; ?>
                  <?php foreach ($filtered_plans as $plan):
                     $type_labels = ['1' => 'Trial', '2' => 'Basic', '3' => 'Premium'];
                     $type_label = isset($type_labels[$plan['subscription_type_id']]) ? $type_labels[$plan['subscription_type_id']] : 'Unknown';
                     $is_trial = $plan['subscription_type_id'] == '1';
                     $is_premium = $plan['subscription_type_id'] == '3';
                  ?>
                  <div class="relative border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:border-teal-300 flex flex-col h-full <?php echo count($filtered_plans) < 4 ? 'w-[320px] flex-shrink-0' : 'w-full min-w-[280px]'; ?> <?php echo $is_premium ? 'ring-2 ring-teal-500' : ''; ?>">
                     <?php if ($is_premium): ?>
                     <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span class="bg-teal-500 text-white px-3 py-1 rounded-full text-xs font-medium">Most Popular</span>
                     </div>
                     <?php endif; ?>

                     <div class="text-center flex flex-col h-full">
                        <!-- Plan Type Badge -->
                        <!-- <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-3 <?php echo $is_trial ? 'bg-orange-100 text-orange-800' : ($is_premium ? 'bg-teal-100 text-teal-800' : 'bg-blue-100 text-blue-800'); ?>">
                           <?php echo esc_html($type_label); ?>
                        </div> -->

                        <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo esc_html($plan['name']); ?></h3>

                        <!-- Price -->
                        <div class="mb-4">
                           <?php if ($plan['regular_price'] && $plan['is_on_sale']): ?>
                              <!-- Sale Price Display -->
                              <div class="text-lg text-gray-500 line-through text-center mb-1">
                                 <?php echo wc_price($plan['regular_price']); ?>/month
                              </div>
                              <div class="text-4xl font-bold text-secondary-main mb-2">
                                 <?php echo wc_price($plan['sale_price']); ?>
                              </div>
                              <!-- <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mb-2">
                                 Sale Price
                              </div> -->
                           <?php else: ?>
                              <!-- Regular Price Display -->
                              <span class="text-4xl font-bold text-gray-900">
                                 <?php echo wc_price($plan['price']); ?>
                              </span>
                           <?php endif; ?>

                           <?php if ($is_trial && $plan['trial_days'] > 0): ?>
                           <!-- <div class="mt-2">
                              <span class="text-sm text-orange-600 font-medium">
                                 <?php echo $plan['trial_days']; ?> days free trial
                              </span>
                              <p class="text-xs text-gray-500 mt-1">
                                 then <?php echo $plan['is_on_sale'] ? wc_price($plan['sale_price']) : wc_price($plan['price']); ?>/month
                              </p>
                           </div> -->
                           <p class="text-sm text-gray-500 mt-1">per month</p>
                           <?php else: ?>
                           <p class="text-sm text-gray-500 mt-1">per month</p>
                           <?php endif; ?>
                        </div>

                        <!-- Description -->
                        <?php if (!empty($plan['short_description'])): ?>
                        <p class="text-sm text-gray-600 mb-6"><?php echo nl2br(esc_html($plan['short_description'])); ?></p>
                        <?php else: ?>
                        <div class="mb-6"></div>
                        <?php endif; ?>

                        <!-- Spacer to push button to bottom -->
                        <div class="flex-grow"></div>

                        <!-- Action Button -->
                        <button class="w-full py-3 px-4 rounded-md font-medium transition-colors select-plan-btn <?php echo $is_premium ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200 border border-gray-300'; ?>"
                                data-plan-id="<?php echo esc_attr($plan['id']); ?>"
                                data-plan-name="<?php echo esc_attr($plan['name']); ?>"
                                data-plan-price="<?php echo esc_attr($plan['is_on_sale'] ? $plan['sale_price'] : $plan['price']); ?>"
                                data-regular-price="<?php echo esc_attr($plan['regular_price']); ?>"
                                data-is-on-sale="<?php echo esc_attr($plan['is_on_sale'] ? '1' : '0'); ?>"
                                data-action="<?php echo esc_attr($action); ?>">
                           <?php echo $action === 'upgrade' ? 'Upgrade' : 'Downgrade'; ?>
                        </button>
                     </div>
                  </div>
                  <?php endforeach; ?>
                  </div>
               </div>
            <?php endif; ?>

            <!-- Features Comparison Section -->
            <div class="px-[16px] py-[48px] md:p-[80px] md:pt-[80px] bg-bg-light mt-12">
                <div class="container mx-auto">
                    <!-- <h2 class="text-lg font-semibold text-gray-900 mb-6 text-center">Plan Features Comparison</h2> -->

                    <!-- Pricing Table with Horizontal Scroll -->
                    <div class="overflow-x-auto">
                        <div class="w-full mx-auto md:w-[1064px]"> <!-- Minimum width to force horizontal scroll on mobile -->
                            <div class="bg-white rounded-[8px] overflow-hidden">
                                <table class="w-full mb-0 rounded-[8px] overflow-hidden border border-dvd-table" id="plan-comparison-table">
                                    <!-- Table Header -->
                                    <thead>
                                        <tr class="bg-brand-color-01">
                                            <th class="py-[16px] md:py-[28px] px-[16px] md:px-[32px] text-left text-16 md:text-24 font-semibold text-eighth-main">Features</th>
                                            <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                                                Plan 1
                                            </th>
                                            <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                                                Plan 2
                                            </th>
                                        </tr>
                                    </thead>
                                    <!-- Table Body -->
                                    <tbody class="divide-y divide-dvd-table divide-solid">
                                        <tr>
                                            <td class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main">Loading features...</td>
                                            <td class="py-[20px] text-14 md:text-18 text-eighth-main text-center">-</td>
                                            <td class="py-[20px] text-14 md:text-18 text-eighth-main text-center">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="border-t border-gray-200 pt-6 mt-8">
               <div class="flex justify-between items-center">
                  <a href="<?php echo esc_url(add_query_arg('customer_page', 'subscription', home_url())); ?>"
                     class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                     <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                     </svg>
                     Back to Subscription
                  </a>

                  <?php if (!empty($filtered_plans)): ?>
                  <div class="text-sm text-gray-500">
                     <?php echo count($filtered_plans); ?> plan<?php echo count($filtered_plans) > 1 ? 's' : ''; ?> available for <?php echo $action; ?>
                  </div>
                  <?php endif; ?>
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle plan selection
    document.querySelectorAll('.select-plan-btn').forEach(button => {
        button.addEventListener('click', function() {
            const planId = this.dataset.planId;
            const planName = this.dataset.planName;
            const planPrice = this.dataset.planPrice;
            const regularPrice = this.dataset.regularPrice;
            const isOnSale = this.dataset.isOnSale === '1';
            const action = this.dataset.action;

            console.log(`${action} selected:`, {
                planId,
                planName,
                planPrice,
                regularPrice,
                isOnSale,
                savings: isOnSale ? (parseFloat(regularPrice) - parseFloat(planPrice)).toFixed(2) : 0
            });
            
            // Handle downgrade vs upgrade differently
            if (action === 'downgrade') {
                // For downgrade: process cancellation directly (no payment needed)
                console.log(`Processing downgrade (cancellation)...`);

                // Show loading state on button
                this.disabled = true;
                this.textContent = 'Processing Downgrade...';

                // Redirect to process downgrade
                window.location.href = '<?php echo esc_js(add_query_arg('customer_page', 'upgrade', home_url())); ?>' +
                                     '&process_downgrade=1' +
                                     '&order_id=' + encodeURIComponent('<?php echo $order_id; ?>');
            } else {
                // For upgrade: go through normal payment flow
                console.log(`Processing ${action} to ${planName}...`);

                // Show loading state on button
                this.disabled = true;
                this.textContent = 'Processing Upgrade...';

                // Redirect to create payment with selected plan (using user-friendly URL)
                const paymentUrl = '<?php echo esc_js(home_url('/customer/create_payment/')); ?>' +
                                 '?plan_change=1' +
                                 '&action=' + encodeURIComponent(action) +
                                 '&new_plan_id=' + encodeURIComponent(planId) +
                                 '&current_order_id=' + encodeURIComponent('<?php echo $order_id; ?>');

                // First, add the new plan to cart
                window.location.href = '<?php echo home_url(); ?>?empty-cart=1&add-to-cart=' + planId +
                                     '&redirect=' + encodeURIComponent(paymentUrl);
            }
        });
    });

    // Features Comparison Table (copied from pricing page)
        const FeatureList = [
            { id: 1, label: 'Storage Limit', type: 'number', unit: 'GB' },
            { id: 2, label: 'Custom Domain Enabled', type: 'checkbox', desc: 'Website address of your choice e.g. www.BestShoeEver.com' },
            { id: 3, label: '1 Year Free Domain Enabled', type: 'checkbox' },
            { id: 4, label: 'SSL Enabled', type: 'checkbox' },
            { id: 5, label: 'AI Assisted Content Enabled', type: 'checkbox' },
            { id: 6, label: 'Mobile Optimized Enabled', type: 'checkbox' },
            { id: 7, label: 'Backup Frequency', type: 'dropdown', options: ['None', 'Daily', 'Weekly', 'Every 10 days'], desc: 'Frequency' },
            { id: 8, label: 'Ads Enabled', type: 'checkbox', desc: 'Show Weaveform Ads' },
            { id: 9, label: 'Contact Form Enabled', type: 'checkbox' },
            { id: 10, label: 'Firewall/Scanner Enabled', type: 'checkbox' },
            { id: 11, label: 'Customer Care Level', type: 'dropdown', options: ['Nil', 'Email (1 day)', 'Priority Support'], desc: 'Support Level' },
        ];

        function featureArrayToMap(features) {
            const map = {};
            features.forEach(f => {
                map[f.id] = f.value;
            });
            return map;
        }

        function renderPlanComparisonTable(plans) {
            if (!plans || plans.length < 2) {
                $('#plan-comparison-table tbody').html('<tr><td colspan="3" class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main text-center">Need at least 2 plans for comparison</td></tr>');
                return;
            }

            // Use first 2 plans for comparison
            const plan1Features = featureArrayToMap(plans[0].features);
            const plan2Features = featureArrayToMap(plans[1].features);

            let html = `
            <table class="w-full mb-0 rounded-[8px] overflow-hidden border border-dvd-table">
                <thead>
                    <tr class="bg-brand-color-01">
                        <th class="py-[16px] md:py-[28px] px-[16px] md:px-[32px] text-left text-16 md:text-24 font-semibold text-eighth-main">Features</th>
                        <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                            ${plans[0].trial_days ? `${plans[0].trial_days} days Trial` : plans[0].name} ${plans[0].price ? `($${plans[0].price})` : ''}
                        </th>
                        <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                            ${plans[1].trial_days ? `${plans[1].trial_days} days Trial` : plans[1].name} ${plans[1].price ? `($${plans[1].price})` : ''}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-dvd-table divide-solid">
            `;

            FeatureList.forEach((feature, idx) => {
                const rowClass = idx % 2 === 1 ? 'bg-table-light' : '';
                let desc = feature.desc ? `<br><span class="text-12 text-neutral-medium">${feature.desc}</span>` : '';

                let value1 = plan1Features[feature.id];
                let value2 = plan2Features[feature.id];

                // Xử lý hiển thị cho từng loại
                if (feature.type === 'checkbox') {
                    value1 = value1 === "true" ? 'Y' : 'N';
                    value2 = value2 === "true" ? 'Y' : 'N';
                } else if (feature.type === 'number' && feature.unit) {
                    value1 = value1 ? value1 + ' ' + feature.unit : '-';
                    value2 = value2 ? value2 + ' ' + feature.unit : '-';
                } else if (feature.type === 'dropdown') {
                    value1 = value1 || '-';
                    value2 = value2 || '-';
                } else {
                    value1 = value1 || '-';
                    value2 = value2 || '-';
                }

                html += `
                    <tr class="${rowClass}">
                        <td class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main left-0">
                            ${feature.label}${desc}
                        </td>
                        <td class="py-[20px] text-14 md:text-18 text-eighth-main text-center">${value1}</td>
                        <td class="py-[20px] text-14 md:text-18 text-eighth-main text-center">${value2}</td>
                    </tr>
                `;
            });

            html += `</tbody></table>`;
            $('#plan-comparison-table').html(html);
        }

        // Get subscription plan IDs from available plans + current plan
        <?php
        $id_subs = [];

        // Add current plan if exists
        if ($current_order) {
            foreach ($current_order->get_items() as $item) {
                $current_product = $item->get_product();
                if ($current_product) {
                    $current_plan_id = $current_product->get_meta('subscription_plan_id');
                    if (!empty($current_plan_id)) {
                        $id_subs[] = $current_plan_id;
                    }
                    break;
                }
            }
        }

        // Add filtered plans (upgrade/downgrade options)
        foreach ($filtered_plans as $plan) {
            if (!empty($plan['subscription_plan_id'])) {
                $id_subs[] = $plan['subscription_plan_id'];
            }
        }

        // Remove duplicates
        $id_subs = array_unique($id_subs);
        ?>

        const planFilters = ["id:[](<?php echo implode(',', $id_subs); ?>)"];
        const queryPlan = `
            query Subscription_plan_list($filters: [String!]) {
                subscription_plan_list(body: { filters: $filters }) {
                    id
                    name
                    desc
                    price
                    features
                    type_id
                    trial_days
                }
            }
        `;

        // Only fetch if we have plan IDs
        <?php if (!empty($id_subs)): ?>
        /* Plan list */
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'ipt_home_graphql',
                query: queryPlan,
                variables: {
                    filters: planFilters
                }
            },
            success: function(response) {
                if(response.data && response.data.subscription_plan_list) {
                    renderPlanComparisonTable(response.data.subscription_plan_list);
                } else {
                    // No data
                    $('#plan-comparison-table tbody').html('<tr><td colspan="3" class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main text-center">No feature data available</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                console.error(error);
                $('#plan-comparison-table tbody').html('<tr><td colspan="3" class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main text-center">Error loading features</td></tr>');
            }
        });
        <?php else: ?>
        // No plans available for comparison
        $('#plan-comparison-table tbody').html('<tr><td colspan="3" class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main text-center">No plans available for comparison</td></tr>');
        <?php endif; ?>
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
