<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- Account Information -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
            <!-- Header with navigation -->
            <!-- <div class="flex items-center mb-6">
               <div class="flex items-center text-sm text-gray-500">
                  <span>Settings</span>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <span>Account Information</span>
               </div>
            </div> -->
            <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/edit_info'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Account</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">Account Information</a>
                  </li>
                  
               </ol>
            </nav>

            <!-- Title -->
            <div class="flex justify-between items-center mb-8">
               <h1 class="text-2xl font-bold">Edit Account Information</h1>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
               <div class="lg:col-span-2">
                  <form id="account-info-form" class="space-y-6">
                     <!-- Account Information Section -->
                     <div class="space-y-4">
                        <div>
                           <label for="first-name" class="block text-sm font-medium text-gray-700 mb-2">First name</label>
                           <input type="text" class="w-full px-3 py-2 bg-blue-50 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:bg-white" id="first-name" placeholder="Enter first name"
                             value="<?php echo esc_attr(wp_get_current_user()->first_name); ?>">
                           <div class="error-message text-red-500 text-xs mt-1 min-h-[18px]" id="first-name-error"></div>
                        </div>

                        <div>
                           <label for="last-name" class="block text-sm font-medium text-gray-700 mb-2">Last name</label>
                           <input type="text" class="w-full px-3 py-2 bg-blue-50 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:bg-white" id="last-name" placeholder="Enter last name"
                             value="<?php echo esc_attr(wp_get_current_user()->last_name); ?>">
                           <div class="error-message text-red-500 text-xs mt-1 min-h-[18px]" id="last-name-error"></div>
                        </div>

                        <div>
                           <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                           <input type="email" class="w-full px-3 py-2 bg-gray-100 border-0 rounded-md text-gray-500 cursor-not-allowed" id="email" placeholder="Email address" disabled readonly
                             value="<?php echo esc_attr(wp_get_current_user()->user_email); ?>" readonly>
                        </div>

                        <div>
                           <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                           <input type="tel" class="w-full px-3 py-2 bg-blue-50 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:bg-white" id="phone" placeholder="Enter phone number"
                             value="<?php echo esc_attr(get_user_meta(wp_get_current_user()->ID, 'phone', true)); ?>"
                             autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false">
                           <div class="error-message text-red-500 text-xs mt-1 min-h-[18px]" id="phone-error"></div>
                        </div>

                        <div class="flex justify-start gap-3 pt-4">
                           <button type="submit" class="px-6 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2" id="save-btn">Update</button>
                        </div>
                     </div>

                     <hr class="border-gray-200">

                     <!-- Change Password Section -->
                     <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900">Change Password</h3>

                        <div>
                           <label for="new-password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                           <div class="relative">
                              <input type="password" class="w-full px-3 py-2 pr-2 bg-blue-50 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:bg-white" id="new-password" placeholder="Enter new password">
                              <button type="button" class="absolute !bg-transparent !outline-none right-1 p-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-500 focus:outline-none password-toggle !shadow-none" aria-label="Toggle password visibility">
                                 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                 </svg>
                              </button>
                           </div>

                           <!-- Password Requirements Hint -->
                           <div class="mt-2 text-xs text-gray-600 hidden" id="password-requirements">
                               <p class="font-medium mb-1">Password must contain:</p>
                               <ul class="space-y-1">
                                   <li class="flex items-center" id="req-length">
                                       <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                           <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                           <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                               <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                           </svg>
                                       </span>
                                       <span class="requirement-text">12 characters or more</span>
                                   </li>
                                   <li class="flex items-center" id="req-lowercase">
                                       <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                           <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                           <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                               <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                           </svg>
                                       </span>
                                       <span class="requirement-text">At least 1 lowercase letter</span>
                                   </li>
                                   <li class="flex items-center" id="req-uppercase">
                                       <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                           <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                           <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                               <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                           </svg>
                                       </span>
                                       <span class="requirement-text">At least 1 uppercase letter</span>
                                   </li>
                                   <li class="flex items-center" id="req-special">
                                       <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                           <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                           <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                               <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                           </svg>
                                       </span>
                                       <span class="requirement-text">At least 1 special character</span>
                                   </li>
                               </ul>
                           </div>

                           <div class="error-message text-red-500 text-xs mt-1 min-h-[18px]" id="new-password-error"></div>
                        </div>

                        <div>
                           <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                           <div class="relative">
                              <input type="password" class="w-full px-3 py-2 pr-2 bg-blue-50 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:bg-white" id="confirm-password" placeholder="Confirm new password">
                              <button type="button" class="absolute !bg-transparent !outline-none right-1  p-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-500 focus:outline-none password-toggle  !shadow-none" aria-label="Toggle password visibility">
                                 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                 </svg>
                              </button>
                           </div>
                           <div class="error-message text-red-500 text-xs mt-1 min-h-[18px]" id="confirm-password-error"></div>
                        </div>

                        <div class="flex justify-start gap-3 pt-4">
                           <button type="submit" class="px-6 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2" id="save-btn-password">Change Password</button>
                        </div>
                     </div>
                  </form>
               </div>

               <div class="lg:col-span-1">
                  <!-- Avatar Section (commented out for now) -->
                  <!-- <div class="text-center">
                     <h3 class="text-lg font-semibold mb-4">Avatar</h3>

                     <?php
                       $user_id = wp_get_current_user()->ID;
                       $avatar_url = get_avatar_url($user_id, array('size' => 150));
                     ?>

                     <div class="mb-4">
                        <img src="<?php echo esc_url($avatar_url); ?>" alt="User Avatar" class="w-30 h-30 rounded-full object-cover mx-auto">
                     </div>

                     <button type="button" class="px-4 py-2 bg-orange-100 text-orange-600 rounded-md hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2" id="change-avatar-btn">Change avatar</button>
                  </div> -->
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<!-- Tailwind CSS styles are applied inline -->

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Password requirements validation
    const newPasswordField = document.getElementById('new-password');
    if (newPasswordField) {
      newPasswordField.addEventListener('input', function() {
        const password = this.value;
        const requirementsDiv = document.getElementById('password-requirements');

        // Show/hide requirements based on password field content
        if (password.length > 0) {
          requirementsDiv.classList.remove('hidden');

          // Check length (12+ characters)
          const hasLength = password.length >= 12;
          updateRequirement('req-length', hasLength);

          // Check lowercase
          const hasLowercase = /[a-z]/.test(password);
          updateRequirement('req-lowercase', hasLowercase);

          // Check uppercase
          const hasUppercase = /[A-Z]/.test(password);
          updateRequirement('req-uppercase', hasUppercase);

          // Check special character
          const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
          updateRequirement('req-special', hasSpecial);
        } else {
          requirementsDiv.classList.add('hidden');
        }
      });
    }

    function updateRequirement(reqId, isValid) {
      const reqElement = document.getElementById(reqId);
      if (!reqElement) return;

      const dot = reqElement.querySelector('.requirement-dot');
      const check = reqElement.querySelector('.requirement-check');
      const text = reqElement.querySelector('.requirement-text');

      if (isValid) {
        dot.classList.add('hidden');
        check.classList.remove('hidden');
        text.classList.add('text-green-600');
        text.classList.remove('text-gray-600');
      } else {
        dot.classList.remove('hidden');
        check.classList.add('hidden');
        text.classList.remove('text-green-600');
        text.classList.add('text-gray-600');
      }
    }

    // Function to show error message
    function showError(fieldId, message) {
      const field = document.getElementById(fieldId);
      const errorElement = document.getElementById(fieldId + '-error');

      if (field && errorElement) {
        field.classList.add('border-red-500', 'bg-red-50');
        field.classList.remove('bg-blue-50', 'focus:bg-white');
        errorElement.textContent = message;
      }
    }

    // Function to clear error message
    function clearError(fieldId) {
      const field = document.getElementById(fieldId);
      const errorElement = document.getElementById(fieldId + '-error');

      if (field && errorElement) {
        field.classList.remove('border-red-500', 'bg-red-50');
        field.classList.add('bg-blue-50', 'focus:bg-white');
        errorElement.textContent = '';
      }
    }

    // Function to clear all errors
    function clearAllErrors() {
      const errorElements = document.querySelectorAll('.error-message');
      const invalidFields = document.querySelectorAll('input');

      errorElements.forEach(element => {
        element.textContent = '';
      });

      invalidFields.forEach(field => {
        field.classList.remove('border-red-500', 'bg-red-50');
        if (!field.disabled) {
          field.classList.add('bg-blue-50', 'focus:bg-white');
        }
      });
    }
    
    // Password validation function
    function validatePassword(password) {
      // Check minimum length
      if (password.length < 12) {
        return {
          valid: false,
          message: 'Password must be at least 12 characters long.'
        };
      }
      
      // Check for uppercase letter
      if (!/[A-Z]/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one uppercase letter.'
        };
      }
      
      // Check for lowercase letter
      if (!/[a-z]/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one lowercase letter.'
        };
      }
      
      // Check for number
      if (!/\d/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one number.'
        };
      }
      
      // Check for special character
      if (!/[^a-zA-Z0-9]/.test(password)) {
        return {
          valid: false,
          message: 'Password must include at least one special character.'
        };
      }
      
      return {
        valid: true
      };
    }
    
    // Function to show success message
    function showSuccessMessage(message) {
      const successMessage = document.createElement('div');
      successMessage.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4';
      successMessage.textContent = message;

      // Insert the success message at the top of the form
      const form = document.getElementById('account-info-form');
      form.parentNode.insertBefore(successMessage, form);

      // Remove the success message after 3 seconds
      setTimeout(() => {
        successMessage.remove();
      }, 3000);
    }

    // Function to show error message (for API errors)
    function showApiError(message) {
      const errorMessage = document.createElement('div');
      errorMessage.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
      errorMessage.textContent = message;

      // Insert the error message at the top of the form
      const form = document.getElementById('account-info-form');
      form.parentNode.insertBefore(errorMessage, form);

      // Remove the error message after 5 seconds
      setTimeout(() => {
        errorMessage.remove();
      }, 5000);
    }
    
    // Function to execute GraphQL mutation via PHP
    function executeGraphQLMutation(mutation, variables = {}) {
      return new Promise((resolve, reject) => {
        jQuery.ajax({
          url: '<?php echo admin_url('admin-ajax.php'); ?>',
          type: 'POST',
          dataType: 'json',
          data: {
            action: 'ipt_home_graphql',
            query: mutation,
            variables: JSON.stringify(variables)
          },
          success: function(response) {
            if (response.errors && response.errors.length > 0) {
              reject(new Error(response.errors[0].message || 'GraphQL error occurred'));
            } else if (response.data) {
              resolve(response.data);
            } else {
              reject(new Error('Invalid response from server'));
            }
          },
          error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            reject(new Error('Failed to connect to server'));
          }
        });
      });
    }
    
    // Function to update WordPress user data
    async function updateWordPressUser(userData) {
      return new Promise((resolve, reject) => {
        jQuery.ajax({
          url: '<?php echo admin_url('admin-ajax.php'); ?>',
          type: 'POST',
          dataType: 'json',
          data: {
            action: 'update_user_profile',
            security: '<?php echo wp_create_nonce("update_user_profile_nonce"); ?>',
            ...userData
          },
          success: function(response) {
            if (response.success) {
              resolve(response.data);
            } else {
              reject(new Error(response.data?.message || 'Failed to update WordPress user data'));
            }
          },
          error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            reject(new Error('Failed to connect to server'));
          }
        });
      });
    }
    
    // Add input event listeners to clear errors when user types
    document.querySelectorAll('input').forEach(input => {
      input.addEventListener('input', function() {
        clearError(this.id);
      });
    });
    
    // Prevent default form submission and handle button clicks separately
    document.getElementById('account-info-form').addEventListener('submit', function(e) {
      e.preventDefault();
    });
    
    // Handle profile info update (save-btn)
    document.getElementById('save-btn').addEventListener('click', async function(e) {
      e.preventDefault();
      
      // Clear all previous errors
      clearAllErrors();
      
      // Get form values for profile info
      const firstName = document.getElementById('first-name').value;
      const lastName = document.getElementById('last-name').value;
      const phone = document.getElementById('phone').value;
      const email = document.getElementById('email').value;
      
      let hasErrors = false;
      
      // Validate first name
      if (!firstName.trim()) {
        showError('first-name', 'First name is required.');
        hasErrors = true;
      }
      
      // Validate last name
      if (!lastName.trim()) {
        showError('last-name', 'Last name is required.');
        hasErrors = true;
      }
      
      // Validate phone (optional validation example)
      if (phone && !/^[0-9+\-\s()]*$/.test(phone)) {
        showError('phone', 'Please enter a valid phone number.');
        hasErrors = true;
      }
      
      // If there are errors, stop form submission
      if (hasErrors) {
        return;
      }
      
      // Show loading state
      const originalButtonText = this.textContent;
      this.textContent = 'Updating...';
      this.disabled = true;
      
      try {
        // Try to update via GraphQL first, but fallback to WordPress-only update if it fails
        let graphqlSuccess = false;

        try {
          // GraphQL mutation for updating user info
          const updateUserMutation = `
            mutation Webhooks_users_update($email: String!, $firstName: String!, $lastName: String!, $phone: String!) {
              webhooks_users_update(
                body: {
                  email: $email,
                  role_id: 2,
                  first_name: $firstName,
                  last_name: $lastName,
                  phone: $phone
                }
              ) {
                email
                id
              }
            }
          `;

          // Variables for mutation
          const variables = {
            email: email,
            firstName: firstName,
            lastName: lastName,
            phone: phone
          };

          // Try GraphQL update
          const result = await executeGraphQLMutation(updateUserMutation, variables);

          if (result && result.webhooks_users_update) {
            graphqlSuccess = true;
          }
        } catch (graphqlError) {
          console.log('GraphQL update failed, proceeding with WordPress-only update:', graphqlError.message);
        }

        // Always update WordPress user data (whether GraphQL succeeded or not)
        await updateWordPressUser({
          first_name: firstName,
          last_name: lastName,
          phone: phone
        });

        // Update displayed values in the form
        document.getElementById('first-name').value = firstName;
        document.getElementById('last-name').value = lastName;
        document.getElementById('phone').value = phone;

        const message = graphqlSuccess
          ? 'Profile information updated successfully!'
          : 'Profile information updated locally. External sync may be pending.';
        showSuccessMessage(message);

      } catch (error) {
        showApiError(error.message || 'An error occurred while updating profile information.');
      } finally {
        // Restore button state
        this.textContent = originalButtonText;
        this.disabled = false;
      }
    });
    
    // Handle password update (save-btn-password)
    document.getElementById('save-btn-password').addEventListener('click', async function(e) {
      e.preventDefault();
      
      // Clear all previous errors
      clearAllErrors();
      
      // Get password values
      const newPassword = document.getElementById('new-password').value;
      const confirmPassword = document.getElementById('confirm-password').value;
      const email = document.getElementById('email').value;
      
      let hasErrors = false;
      
      // Check if both fields are filled
      if (!newPassword) {
        showError('new-password', 'Please enter a new password.');
        hasErrors = true;
      }
      
      if (!confirmPassword) {
        showError('confirm-password', 'Please confirm your new password.');
        hasErrors = true;
      }
      
      // Check if passwords match
      if (newPassword && confirmPassword && newPassword !== confirmPassword) {
        showError('confirm-password', 'Passwords do not match.');
        hasErrors = true;
      }
      
      // Validate password against rules
      if (newPassword) {
        const passwordValidation = validatePassword(newPassword);
        if (!passwordValidation.valid) {
          showError('new-password', passwordValidation.message);
          hasErrors = true;
        }
      }
      
      // If there are errors, stop form submission
      if (hasErrors) {
        return;
      }
      
      // Show loading state
      const originalButtonText = this.textContent;
      this.textContent = 'Updating...';
      this.disabled = true;
      
      try {
        // GraphQL mutation for updating password - Sử dụng biến thay vì nhúng trực tiếp
        const updatePasswordMutation = `
          mutation Webhooks_users_update($email: String!, $password: String!) {
            webhooks_users_update(
              body: { 
                email: $email, 
                role_id: 2, 
                password: $password 
              }
            ) {
              email
              id
            }
          }
        `;
        
        // Biến cho mutation
        const variables = {
          email: email,
          password: newPassword
        };
        
        // Try to update via GraphQL first, but fallback to WordPress-only update if it fails
        let graphqlSuccess = false;

        try {
          // Try GraphQL update
          const result = await executeGraphQLMutation(updatePasswordMutation, variables);

          if (result && result.webhooks_users_update) {
            graphqlSuccess = true;
          }
        } catch (graphqlError) {
          console.log('GraphQL password update failed, proceeding with WordPress-only update:', graphqlError.message);
        }

        // Always update WordPress user password (whether GraphQL succeeded or not)
        await updateWordPressUser({
          password: newPassword
        });

        const message = graphqlSuccess
          ? 'Password changed successfully!'
          : 'Password changed locally. External sync may be pending.';
        showSuccessMessage(message);

        // Clear password fields after successful update
        document.getElementById('new-password').value = '';
        document.getElementById('confirm-password').value = '';

      } catch (error) {
        showApiError(error.message || 'An error occurred while updating password.');
      } finally {
        // Restore button state
        this.textContent = originalButtonText;
        this.disabled = false;
      }
    });
    
    // Toggle password visibility
    document.querySelectorAll('.password-toggle').forEach(function(button) {
      button.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const svg = this.querySelector('svg');

        if (input.type === 'password') {
          input.type = 'text';
          // Change to eye-slash icon (hidden)
          svg.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
          `;
        } else {
          input.type = 'password';
          // Change to eye icon (visible)
          svg.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          `;
        }
      });
    });
  });
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
