*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-top-3 {
  top: -0.75rem;
}

.left-0 {
  left: 0px;
}

.left-1 {
  left: 0.25rem;
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: 0.5rem;
}

.left-4 {
  left: 1rem;
}

.right-0 {
  right: 0px;
}

.right-1 {
  right: 0.25rem;
}

.right-2 {
  right: 0.5rem;
}

.right-3 {
  right: 0.75rem;
}

.right-4 {
  right: 1rem;
}

.right-6 {
  right: 1.5rem;
}

.top-0 {
  top: 0px;
}

.top-1 {
  top: 0.25rem;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: 0.5rem;
}

.top-2\.5 {
  top: 0.625rem;
}

.top-4 {
  top: 1rem;
}

.top-\[84px\] {
  top: 84px;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[1\] {
  z-index: 1;
}

.z-\[9998\] {
  z-index: 9998;
}

.z-\[999\] {
  z-index: 999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-full {
  grid-column: 1 / -1;
}

.m-0 {
  margin: 0px;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-\[16px\] {
  margin-left: 16px;
  margin-right: 16px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.-ml-1 {
  margin-left: -0.25rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-3\.5 {
  margin-bottom: 0.875rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-\[12px\] {
  margin-bottom: 12px;
}

.mb-\[168px\] {
  margin-bottom: 168px;
}

.mb-\[44px\] {
  margin-bottom: 44px;
}

.ml-0 {
  margin-left: 0px;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[119px\] {
  margin-top: 119px;
}

.mt-\[16px\] {
  margin-top: 16px;
}

.mt-\[25px\] {
  margin-top: 25px;
}

.mt-\[29px\] {
  margin-top: 29px;
}

.mt-\[32px\] {
  margin-top: 32px;
}

.mt-\[48px\] {
  margin-top: 48px;
}

.mt-\[50px\] {
  margin-top: 50px;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.aspect-\[1\.7\] {
  aspect-ratio: 1.7;
}

.aspect-\[1\] {
  aspect-ratio: 1;
}

.\!h-\[18px\] {
  height: 18px !important;
}

.\!h-\[48px\] {
  height: 48px !important;
}

.\!h-full {
  height: 100% !important;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[18px\] {
  height: 18px;
}

.h-\[200px\] {
  height: 200px;
}

.h-\[24px\] {
  height: 24px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[48px\] {
  height: 48px;
}

.h-\[56px\] {
  height: 56px;
}

.h-\[5px\] {
  height: 5px;
}

.h-\[68px\] {
  height: 68px;
}

.h-\[84px\] {
  height: 84px;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[40px\] {
  max-height: 40px;
}

.max-h-\[84px\] {
  max-height: 84px;
}

.min-h-14 {
  min-height: 3.5rem;
}

.min-h-6 {
  min-height: 1.5rem;
}

.min-h-\[18px\] {
  min-height: 18px;
}

.min-h-\[220px\] {
  min-height: 220px;
}

.min-h-\[300px\] {
  min-height: 300px;
}

.min-h-\[32px\] {
  min-height: 32px;
}

.min-h-\[401px\] {
  min-height: 401px;
}

.min-h-\[40px\] {
  min-height: 40px;
}

.min-h-\[700px\] {
  min-height: 700px;
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.\!w-\[18px\] {
  width: 18px !important;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-3 {
  width: 0.75rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/12 {
  width: 41.666667%;
}

.w-6 {
  width: 1.5rem;
}

.w-7\/12 {
  width: 58.333333%;
}

.w-8 {
  width: 2rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[100px\] {
  width: 100px;
}

.w-\[129px\] {
  width: 129px;
}

.w-\[18px\] {
  width: 18px;
}

.w-\[196px\] {
  width: 196px;
}

.w-\[220px\] {
  width: 220px;
}

.w-\[24px\] {
  width: 24px;
}

.w-\[250px\] {
  width: 250px;
}

.w-\[305px\] {
  width: 305px;
}

.w-\[320px\] {
  width: 320px;
}

.w-\[32px\] {
  width: 32px;
}

.w-\[34px\] {
  width: 34px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[588px\] {
  width: 588px;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[68px\] {
  width: 68px;
}

.w-\[848px\] {
  width: 848px;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.w-64 {
  width: 16rem;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[180px\] {
  min-width: 180px;
}

.min-w-\[220px\] {
  min-width: 220px;
}

.min-w-\[280px\] {
  min-width: 280px;
}

.min-w-\[340px\] {
  min-width: 340px;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[1064px\] {
  max-width: 1064px;
}

.max-w-\[1296px\] {
  max-width: 1296px;
}

.max-w-\[196px\] {
  max-width: 196px;
}

.max-w-\[520px\] {
  max-width: 520px;
}

.max-w-\[528px\] {
  max-width: 528px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-\[1_0_0\] {
  flex: 1 0 0;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.resize-none {
  resize: none;
}

.list-none {
  list-style-type: none;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-0 {
  gap: 0px;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[16px\] {
  gap: 16px;
}

.gap-\[20px\] {
  gap: 20px;
}

.gap-\[24px\] {
  gap: 24px;
}

.gap-\[32px\] {
  gap: 32px;
}

.gap-\[48px\] {
  gap: 48px;
}

.gap-\[4px\] {
  gap: 4px;
}

.gap-\[5px\] {
  gap: 5px;
}

.gap-\[64px\] {
  gap: 64px;
}

.gap-\[80px\] {
  gap: 80px;
}

.gap-\[8px\] {
  gap: 8px;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-solid > :not([hidden]) ~ :not([hidden]) {
  border-style: solid;
}

.divide-dvd-table > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(230 233 245 / var(--tw-divide-opacity, 1));
}

.self-stretch {
  align-self: stretch;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.\!rounded-full {
  border-radius: 9999px !important;
}

.\!rounded-md {
  border-radius: 0.375rem !important;
}

.\!rounded-none {
  border-radius: 0px !important;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[12px\] {
  border-radius: 12px;
}

.rounded-\[16px\] {
  border-radius: 16px;
}

.rounded-\[30px\] {
  border-radius: 30px;
}

.rounded-\[6px\] {
  border-radius: 6px;
}

.rounded-\[80px\] {
  border-radius: 80px;
}

.rounded-\[8px\] {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-\[1px\] {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-t {
  border-top-width: 1px;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.\!border-none {
  border-style: none !important;
}

.border-none {
  border-style: none;
}

.\!border-fourth-main {
  --tw-border-opacity: 1 !important;
  border-color: rgb(216 216 216 / var(--tw-border-opacity, 1)) !important;
}

.border-\[\#E6E8EC\] {
  --tw-border-opacity: 1;
  border-color: rgb(230 232 236 / var(--tw-border-opacity, 1));
}

.border-\[\#E6E9F5\] {
  --tw-border-opacity: 1;
  border-color: rgb(230 233 245 / var(--tw-border-opacity, 1));
}

.border-\[\#E8E8E8\] {
  --tw-border-opacity: 1;
  border-color: rgb(232 232 232 / var(--tw-border-opacity, 1));
}

.border-\[none\] {
  border-color: none;
}

.border-bd-border {
  --tw-border-opacity: 1;
  border-color: rgb(214 230 254 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-brand-main {
  --tw-border-opacity: 1;
  border-color: rgb(72 201 176 / var(--tw-border-opacity, 1));
}

.border-btn-disable {
  --tw-border-opacity: 1;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}

.border-dvd-table {
  --tw-border-opacity: 1;
  border-color: rgb(230 233 245 / var(--tw-border-opacity, 1));
}

.border-fifth-main {
  --tw-border-opacity: 1;
  border-color: rgb(221 225 230 / var(--tw-border-opacity, 1));
}

.border-fourth-main {
  --tw-border-opacity: 1;
  border-color: rgb(216 216 216 / var(--tw-border-opacity, 1));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-neutral-light {
  --tw-border-opacity: 1;
  border-color: rgb(141 141 141 / var(--tw-border-opacity, 1));
}

.border-neutral-medium {
  --tw-border-opacity: 1;
  border-color: rgb(216 216 216 / var(--tw-border-opacity, 1));
}

.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-b-\[\#EFEFEF\] {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
}

.\!bg-brand-main {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-color-disabled-2 {
  background-color: rgba(0,0,0,0.063) !important;
}

.\!bg-dark-blue {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(0 39 111 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-db-dark {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(19 23 32 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-ipt-bg-1 {
  background-color: #006EFF10 !important;
}

.\!bg-neutral-subtle {
  background-color: #00000010 !important;
}

.\!bg-transparent {
  background-color: transparent !important;
}

.\!bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#001A2C\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 26 44 / var(--tw-bg-opacity, 1));
}

.bg-\[\#48C9B0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F2F4F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(242 244 248 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F7F9FA\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 249 250 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F9F9F9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 249 249 / var(--tw-bg-opacity, 1));
}

.bg-\[rgba\(255\2c 255\2c 255\2c 0\.59\)\] {
  background-color: rgba(255,255,255,0.59);
}

.bg-\[rgba\(72\2c 201\2c 176\2c 1\)\] {
  background-color: rgba(72,201,176,1);
}

.bg-bg-light {
  --tw-bg-opacity: 1;
  background-color: rgb(247 249 250 / var(--tw-bg-opacity, 1));
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-brand-color-01 {
  --tw-bg-opacity: 1;
  background-color: rgb(218 239 235 / var(--tw-bg-opacity, 1));
}

.bg-brand-main {
  --tw-bg-opacity: 1;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1));
}

.bg-brand-main-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(35 50 123 / var(--tw-bg-opacity, 1));
}

.bg-brand-main\/5 {
  background-color: rgb(72 201 176 / 0.05);
}

.bg-brand-main\/80 {
  background-color: rgb(72 201 176 / 0.8);
}

.bg-btn-disable {
  --tw-bg-opacity: 1;
  background-color: rgb(224 224 224 / var(--tw-bg-opacity, 1));
}

.bg-db-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(19 23 32 / var(--tw-bg-opacity, 1));
}

.bg-db-dark-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 249 249 / var(--tw-bg-opacity, 1));
}

.bg-db-dark-light {
  background-color: #FFFFFF0D;
}

.bg-db-green {
  --tw-bg-opacity: 1;
  background-color: rgb(143 202 19 / var(--tw-bg-opacity, 1));
}

.bg-disable-step {
  --tw-bg-opacity: 1;
  background-color: rgb(217 217 217 / var(--tw-bg-opacity, 1));
}

.bg-footer-main {
  --tw-bg-opacity: 1;
  background-color: rgb(93 173 226 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-ipt-bg-1 {
  background-color: #006EFF10;
}

.bg-label-success {
  --tw-bg-opacity: 1;
  background-color: rgb(0 135 255 / var(--tw-bg-opacity, 1));
}

.bg-neutral-light {
  --tw-bg-opacity: 1;
  background-color: rgb(141 141 141 / var(--tw-bg-opacity, 1));
}

.bg-neutral-strong {
  --tw-bg-opacity: 1;
  background-color: rgb(32 32 32 / var(--tw-bg-opacity, 1));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.bg-stone-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 249 / var(--tw-bg-opacity, 1));
}

.bg-table-light {
  background-color: #00000008;
}

.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}

.bg-teal-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}

.bg-third-main {
  --tw-bg-opacity: 1;
  background-color: rgb(242 244 248 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-black\/70 {
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-black\/30 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.\!p-2 {
  padding: 0.5rem !important;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-20 {
  padding: 5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[12px\] {
  padding: 12px;
}

.p-\[16px\] {
  padding: 16px;
}

.p-\[20px\] {
  padding: 20px;
}

.\!px-\[16px\] {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.\!py-\[12px\] {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px;
}

.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px;
}

.px-\[16px\] {
  padding-left: 16px;
  padding-right: 16px;
}

.px-\[20px\] {
  padding-left: 20px;
  padding-right: 20px;
}

.px-\[24px\] {
  padding-left: 24px;
  padding-right: 24px;
}

.px-\[74px\] {
  padding-left: 74px;
  padding-right: 74px;
}

.px-\[8px\] {
  padding-left: 8px;
  padding-right: 8px;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[12px\] {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-\[16px\] {
  padding-top: 16px;
  padding-bottom: 16px;
}

.py-\[20px\] {
  padding-top: 20px;
  padding-bottom: 20px;
}

.py-\[2px\] {
  padding-top: 2px;
  padding-bottom: 2px;
}

.py-\[48px\] {
  padding-top: 48px;
  padding-bottom: 48px;
}

.py-\[72px\] {
  padding-top: 72px;
  padding-bottom: 72px;
}

.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}

.\!pl-10 {
  padding-left: 2.5rem !important;
}

.\!pl-12 {
  padding-left: 3rem !important;
}

.\!pl-\[16px\] {
  padding-left: 16px !important;
}

.\!pr-\[40px\] {
  padding-right: 40px !important;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-\[48px\] {
  padding-bottom: 48px;
}

.pb-\[60px\] {
  padding-bottom: 60px;
}

.pl-0 {
  padding-left: 0px;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pl-\[16px\] {
  padding-left: 16px;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-\[138px\] {
  padding-top: 138px;
}

.pt-\[16px\] {
  padding-top: 16px;
}

.pt-\[210px\] {
  padding-top: 210px;
}

.pt-\[73px\] {
  padding-top: 73px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.font-inter {
  font-family: Inter;
}

.text-12 {
  font-size: 12px;
  line-height: 140%;
}

.text-13 {
  font-size: 13px;
  line-height: 140%;
}

.text-14 {
  font-size: 14px;
  line-height: 140%;
}

.text-16 {
  font-size: 16px;
  line-height: 140%;
}

.text-16-2 {
  font-size: 16px;
  line-height: 0;
}

.text-18 {
  font-size: 18px;
  line-height: 140%;
}

.text-20 {
  font-size: 20px;
  line-height: 110%;
}

.text-24 {
  font-size: 24px;
  line-height: 110%;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-32 {
  font-size: 32px;
  line-height: 110%;
}

.text-34 {
  font-size: 34px;
  line-height: 110%;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-42 {
  font-size: 42px;
  line-height: 110%;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-54 {
  font-size: 54px;
  line-height: 110%;
}

.text-56 {
  font-size: 56px;
  line-height: 64px;
}

.text-\[100px\] {
  font-size: 100px;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[32px\] {
  font-size: 32px;
}

.text-\[45px\] {
  font-size: 45px;
}

.text-\[54px\] {
  font-size: 54px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-\[1\.1\] {
  line-height: 1.1;
}

.leading-\[26px\] {
  line-height: 26px;
}

.leading-\[35\.2px\] {
  line-height: 35.2px;
}

.leading-\[46\.2px\] {
  line-height: 46.2px;
}

.leading-\[58\.95px\] {
  line-height: 58.95px;
}

.leading-\[59\.4px\] {
  line-height: 59.4px;
}

.leading-none {
  line-height: 1;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-\[-0\.14px\] {
  letter-spacing: -0.14px;
}

.tracking-\[-0\.16px\] {
  letter-spacing: -0.16px;
}

.tracking-\[1px\] {
  letter-spacing: 1px;
}

.tracking-normal {
  letter-spacing: 0em;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-tighter {
  letter-spacing: -0.05em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.\!text-brand-main {
  --tw-text-opacity: 1 !important;
  color: rgb(72 201 176 / var(--tw-text-opacity, 1)) !important;
}

.\!text-gray-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1)) !important;
}

.\!text-neutral-strong {
  --tw-text-opacity: 1 !important;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1)) !important;
}

.\!text-primary-main {
  color: #000000E8 !important;
}

.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.text-\[\#0A1734\] {
  --tw-text-opacity: 1;
  color: rgb(10 23 52 / var(--tw-text-opacity, 1));
}

.text-\[\#202020\] {
  --tw-text-opacity: 1;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1));
}

.text-\[\#21272A\] {
  --tw-text-opacity: 1;
  color: rgb(33 39 42 / var(--tw-text-opacity, 1));
}

.text-\[\#8D8D8D\] {
  --tw-text-opacity: 1;
  color: rgb(141 141 141 / var(--tw-text-opacity, 1));
}

.text-\[\#BBB\] {
  --tw-text-opacity: 1;
  color: rgb(187 187 187 / var(--tw-text-opacity, 1));
}

.text-\[rgba\(0\2c 0\2c 0\2c 0\.91\)\] {
  color: rgba(0,0,0,0.91);
}

.text-\[rgba\(255\2c 255\2c 255\2c 0\.75\)\] {
  color: rgba(255,255,255,0.75);
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-brand-main {
  --tw-text-opacity: 1;
  color: rgb(72 201 176 / var(--tw-text-opacity, 1));
}

.text-color-disabled {
  --tw-text-opacity: 1;
  color: rgb(187 187 187 / var(--tw-text-opacity, 1));
}

.text-db-dark {
  --tw-text-opacity: 1;
  color: rgb(19 23 32 / var(--tw-text-opacity, 1));
}

.text-db-text {
  --tw-text-opacity: 1;
  color: rgb(0 6 36 / var(--tw-text-opacity, 1));
}

.text-eighth-main {
  --tw-text-opacity: 1;
  color: rgb(37 36 48 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.text-neutral-light {
  --tw-text-opacity: 1;
  color: rgb(141 141 141 / var(--tw-text-opacity, 1));
}

.text-neutral-medium {
  --tw-text-opacity: 1;
  color: rgb(216 216 216 / var(--tw-text-opacity, 1));
}

.text-neutral-strong {
  --tw-text-opacity: 1;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-primary-main {
  color: #000000E8;
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-remove-text {
  --tw-text-opacity: 1;
  color: rgb(162 169 176 / var(--tw-text-opacity, 1));
}

.text-secondary-main {
  --tw-text-opacity: 1;
  color: rgb(33 39 42 / var(--tw-text-opacity, 1));
}

.text-seventh-main {
  --tw-text-opacity: 1;
  color: rgb(0 29 108 / var(--tw-text-opacity, 1));
}

.text-teal-800 {
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.opacity-80 {
  opacity: 0.8;
}

.bg-blend-normal {
  background-blend-mode: normal;
}

.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_24px_-4px_rgba\(0\2c 0\2c 0\2c 0\.07\)\] {
  --tw-shadow: 0px 0px 24px -4px rgba(0,0,0,0.07);
  --tw-shadow-colored: 0px 0px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-elevation-bottom-100 {
  --tw-shadow: 0px 0px 24px -4px #00000012;
  --tw-shadow-colored: 0px 0px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.\!outline-none {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-teal-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1));
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item {
  border-bottom: 2px solid transparent;
  transition: border-color 0.2s;
}

.nav-item:hover, .nav-item.active {
  border-bottom-color: #2BA990;
}

.placeholder\:text-gray-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.placeholder\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.placeholder\:text-neutral-light::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(141 141 141 / var(--tw-text-opacity, 1));
}

.placeholder\:text-neutral-light::placeholder {
  --tw-text-opacity: 1;
  color: rgb(141 141 141 / var(--tw-text-opacity, 1));
}

.placeholder\:text-neutral-medium::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(216 216 216 / var(--tw-text-opacity, 1));
}

.placeholder\:text-neutral-medium::placeholder {
  --tw-text-opacity: 1;
  color: rgb(216 216 216 / var(--tw-text-opacity, 1));
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}

.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.checked\:border-brand-main:checked {
  --tw-border-opacity: 1;
  border-color: rgb(72 201 176 / var(--tw-border-opacity, 1));
}

.checked\:bg-brand-main:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1));
}

.checked\:ring-brand-main:checked {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(72 201 176 / var(--tw-ring-opacity, 1));
}

.hover\:border-bd-border:hover {
  --tw-border-opacity: 1;
  border-color: rgb(214 230 254 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:border-brand-main:hover {
  --tw-border-opacity: 1;
  border-color: rgb(72 201 176 / var(--tw-border-opacity, 1));
}

.hover\:border-btn-disable:hover {
  --tw-border-opacity: 1;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}

.hover\:border-green-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-medium:hover {
  --tw-border-opacity: 1;
  border-color: rgb(216 216 216 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}

.hover\:\!bg-brand-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1)) !important;
}

.hover\:\!bg-brand-orange:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 111 97 / var(--tw-bg-opacity, 1)) !important;
}

.hover\:\!bg-transparent:hover {
  background-color: transparent !important;
}

.hover\:bg-\[\#3ab19a\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(58 177 154 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[rgba\(62\2c 181\2c 156\2c 1\)\]:hover {
  background-color: rgba(62,181,156,1);
}

.hover\:bg-brand-color-01:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(218 239 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-brand-main:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1));
}

.hover\:bg-brand-main\/10:hover {
  background-color: rgb(72 201 176 / 0.1);
}

.hover\:bg-brand-main\/80:hover {
  background-color: rgb(72 201 176 / 0.8);
}

.hover\:bg-btn-disable:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(224 224 224 / var(--tw-bg-opacity, 1));
}

.hover\:bg-dark-blue:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 39 111 / var(--tw-bg-opacity, 1));
}

.hover\:bg-db-dark-light:hover {
  background-color: #FFFFFF0D;
}

.hover\:bg-db-green:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(143 202 19 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-ipt-bg-1:hover {
  background-color: #006EFF10;
}

.hover\:bg-neutral-light-7:hover {
  background-color: #00000012;
}

.hover\:bg-neutral-strong\/80:hover {
  background-color: rgb(32 32 32 / 0.8);
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}

.hover\:bg-transparent:hover {
  background-color: transparent;
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:\!text-brand-main:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(72 201 176 / var(--tw-text-opacity, 1)) !important;
}

.hover\:\!text-color-disabled:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(187 187 187 / var(--tw-text-opacity, 1)) !important;
}

.hover\:\!text-neutral-strong:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1)) !important;
}

.hover\:\!text-primary-main:hover {
  color: #000000E8 !important;
}

.hover\:\!text-white:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.hover\:text-brand-main:hover {
  --tw-text-opacity: 1;
  color: rgb(72 201 176 / var(--tw-text-opacity, 1));
}

.hover\:text-brand-main\/80:hover {
  color: rgb(72 201 176 / 0.8);
}

.hover\:text-color-disabled:hover {
  --tw-text-opacity: 1;
  color: rgb(187 187 187 / var(--tw-text-opacity, 1));
}

.hover\:text-db-text:hover {
  --tw-text-opacity: 1;
  color: rgb(0 6 36 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-strong:hover {
  --tw-text-opacity: 1;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-main\/80:hover {
  color: rgb(0 0 0 / 0.8);
}

.hover\:text-teal-500:hover {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:text-white\/80:hover {
  color: rgb(255 255 255 / 0.8);
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:\!outline-none:hover {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

.focus\:border-bd-border:focus {
  --tw-border-opacity: 1;
  border-color: rgb(214 230 254 / var(--tw-border-opacity, 1));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-brand-main:focus {
  --tw-border-opacity: 1;
  border-color: rgb(72 201 176 / var(--tw-border-opacity, 1));
}

.focus\:border-white:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.focus\:\!bg-brand-main:focus {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1)) !important;
}

.focus\:bg-brand-main:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1));
}

.focus\:bg-brand-main\/80:focus {
  background-color: rgb(72 201 176 / 0.8);
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.focus\:\!text-brand-main:focus {
  --tw-text-opacity: 1 !important;
  color: rgb(72 201 176 / var(--tw-text-opacity, 1)) !important;
}

.focus\:\!text-neutral-strong:focus {
  --tw-text-opacity: 1 !important;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1)) !important;
}

.focus\:text-neutral-strong:focus {
  --tw-text-opacity: 1;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1));
}

.focus\:text-white:focus {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\:\!outline-none:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-brand-main:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(72 201 176 / var(--tw-ring-opacity, 1));
}

.focus\:ring-emerald-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(52 211 153 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}

.focus\:ring-teal-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.active\:border-bd-border:active {
  --tw-border-opacity: 1;
  border-color: rgb(214 230 254 / var(--tw-border-opacity, 1));
}

.active\:\!bg-brand-main:active {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1)) !important;
}

.active\:bg-brand-main:active {
  --tw-bg-opacity: 1;
  background-color: rgb(72 201 176 / var(--tw-bg-opacity, 1));
}

.active\:bg-brand-main\/80:active {
  background-color: rgb(72 201 176 / 0.8);
}

.active\:\!text-neutral-strong:active {
  --tw-text-opacity: 1 !important;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1)) !important;
}

.active\:text-neutral-strong:active {
  --tw-text-opacity: 1;
  color: rgb(32 32 32 / var(--tw-text-opacity, 1));
}

.active\:text-white:active {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.active\:\!outline-none:active {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

.active\:outline-none:active {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.peer:checked ~ .peer-checked\:translate-x-4 {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus ~ .peer-focus\:ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}

.peer:focus ~ .peer-focus\:ring-green-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(134 239 172 / var(--tw-ring-opacity, 1));
}

@media not all and (min-width: 768px) {
  .max-md\:mr-\[7px\] {
    margin-right: 7px;
  }

  .max-md\:mt-10 {
    margin-top: 2.5rem;
  }

  .max-md\:hidden {
    display: none;
  }

  .max-md\:w-full {
    width: 100%;
  }

  .max-md\:max-w-\[500px\] {
    max-width: 500px;
  }

  .max-md\:max-w-full {
    max-width: 100%;
  }

  .max-md\:flex-col {
    flex-direction: column;
  }

  .max-md\:flex-wrap {
    flex-wrap: wrap;
  }

  .max-md\:items-start {
    align-items: flex-start;
  }

  .max-md\:items-stretch {
    align-items: stretch;
  }

  .max-md\:gap-10 {
    gap: 2.5rem;
  }

  .max-md\:p-8 {
    padding: 2rem;
  }

  .max-md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .max-md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .max-md\:pt-\[100px\] {
    padding-top: 100px;
  }

  .max-md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media not all and (min-width: 640px) {
  .max-sm\:w-full {
    width: 100%;
  }

  .max-sm\:min-w-\[100px\] {
    min-width: 100px;
  }

  .max-sm\:max-w-none {
    max-width: none;
  }

  .max-sm\:gap-4 {
    gap: 1rem;
  }

  .max-sm\:gap-8 {
    gap: 2rem;
  }

  .max-sm\:rounded {
    border-radius: 0.25rem;
  }

  .max-sm\:p-3 {
    padding: 0.75rem;
  }

  .max-sm\:p-6 {
    padding: 1.5rem;
  }

  .max-sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .max-sm\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .max-sm\:px-\[16px\] {
    padding-left: 16px;
    padding-right: 16px;
  }

  .max-sm\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .max-sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .max-sm\:pb-3 {
    padding-bottom: 0.75rem;
  }

  .max-sm\:pt-0 {
    padding-top: 0px;
  }

  .max-sm\:text-24 {
    font-size: 24px;
    line-height: 110%;
  }

  .max-sm\:text-32 {
    font-size: 32px;
    line-height: 110%;
  }

  .max-sm\:text-\[28px\] {
    font-size: 28px;
  }

  .max-sm\:text-\[32px\] {
    font-size: 32px;
  }

  .max-sm\:text-\[42px\] {
    font-size: 42px;
  }

  .max-sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .max-sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .max-sm\:leading-5 {
    line-height: 1.25rem;
  }
}

@media (min-width: 640px) {
  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:\!rounded-none {
    border-radius: 0px !important;
  }

  .sm\:text-left {
    text-align: left;
  }
}

@media (min-width: 768px) {
  .md\:static {
    position: static;
  }

  .md\:z-auto {
    z-index: auto;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:mt-\[100px\] {
    margin-top: 100px;
  }

  .md\:mt-\[20px\] {
    margin-top: 20px;
  }

  .md\:mt-\[40px\] {
    margin-top: 40px;
  }

  .md\:mt-\[60px\] {
    margin-top: 60px;
  }

  .md\:mt-\[80px\] {
    margin-top: 80px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[48px\] {
    height: 48px;
  }

  .md\:h-\[56px\] {
    height: 56px;
  }

  .md\:h-\[84px\] {
    height: 84px;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:w-\[1064px\] {
    width: 1064px;
  }

  .md\:w-\[106px\] {
    width: 106px;
  }

  .md\:w-\[196px\] {
    width: 196px;
  }

  .md\:w-\[412px\] {
    width: 412px;
  }

  .md\:w-\[630px\] {
    width: 630px;
  }

  .md\:w-\[886px\] {
    width: 886px;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:min-w-\[220px\] {
    min-width: 220px;
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:gap-\[16px\] {
    gap: 16px;
  }

  .md\:gap-\[20px\] {
    gap: 20px;
  }

  .md\:gap-\[28px\] {
    gap: 28px;
  }

  .md\:gap-\[48px\] {
    gap: 48px;
  }

  .md\:gap-\[64px\] {
    gap: 64px;
  }

  .md\:p-\[80px\] {
    padding: 80px;
  }

  .md\:px-\[16px\] {
    padding-left: 16px;
    padding-right: 16px;
  }

  .md\:px-\[32px\] {
    padding-left: 32px;
    padding-right: 32px;
  }

  .md\:px-\[80px\] {
    padding-left: 80px;
    padding-right: 80px;
  }

  .md\:py-\[28px\] {
    padding-top: 28px;
    padding-bottom: 28px;
  }

  .md\:py-\[32px\] {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .md\:pl-\[188px\] {
    padding-left: 188px;
  }

  .md\:pl-\[80px\] {
    padding-left: 80px;
  }

  .md\:pr-\[128px\] {
    padding-right: 128px;
  }

  .md\:pt-\[160px\] {
    padding-top: 160px;
  }

  .md\:pt-\[24px\] {
    padding-top: 24px;
  }

  .md\:pt-\[80px\] {
    padding-top: 80px;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-16 {
    font-size: 16px;
    line-height: 140%;
  }

  .md\:text-18 {
    font-size: 18px;
    line-height: 140%;
  }

  .md\:text-20 {
    font-size: 20px;
    line-height: 110%;
  }

  .md\:text-24 {
    font-size: 24px;
    line-height: 110%;
  }

  .md\:text-32 {
    font-size: 32px;
    line-height: 110%;
  }

  .md\:text-42 {
    font-size: 42px;
    line-height: 110%;
  }

  .md\:text-44 {
    font-size: 44px;
    line-height: 110%;
  }
}

@media (min-width: 1024px) {
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .lg\:w-\[196px\] {
    width: 196px;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:gap-\[24px\] {
    gap: 24px;
  }

  .lg\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }
}

@media (min-width: 1280px) {
  .xl\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .xl\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}