<?php
/**
 * Template Name: Register page template
 *
 * @package ipt_home
 */

get_header();

// Ensure toast.js is loaded
wp_enqueue_script('ipt-toast');
?>
<main class="w-full bg-white">
    <div class="max-sm:px-[16px]  mt-[48px] md:mt-[60px] mx-auto w-full md:w-[630px] mb-[168px]">
        <h1 class="text-left text-24 md:text-32 font-bold text-black mb-[44px] font-semibold">Sign up</h1>
        <div class="shadow-none"> 
            <form class="space-y-6" id="registerForm">
                <!-- Email Input -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="email" class="block text-[20px] font-medium text-[#21272A] mb-2">Email</label>
                        <div class="mt-1">
                            <input id="email" 
                                name="email" 
                                type="email" 
                                required 
                                class="appearance-none block w-full px-4 py-3 border border-[#E6E9F5] rounded-lg shadow-sm 
                                        placeholder-gray-400 focus:outline-none focus:border-brand-main focus:ring-1 
                                        focus:ring-brand-main !text-neutral-strong !bg-ipt-bg-1 !h-[48px]"
                                placeholder="<EMAIL>">
                            <p id="email-error" class="mt-1 text-red-500 text-sm hidden">Please enter your email</p>
                        </div>
                    </div>

                   <div>
                        <label for="confirm_email" class="block text-[20px] font-medium text-[#21272A] mb-2">Confirm Email</label>
                        <div class="mt-1">
                            <input id="confirm_email" 
                                name="confirm_email" 
                                type="email" 
                                required 
                                class="appearance-none block w-full px-4 py-3 border border-[#E6E9F5] rounded-lg shadow-sm 
                                        placeholder-gray-400 focus:outline-none focus:border-brand-main focus:ring-1 
                                        focus:ring-brand-main !text-neutral-strong !bg-ipt-bg-1 !h-[48px]"
                                placeholder="">
                            <p id="confirm-email-error" class="mt-1 text-red-500 text-sm hidden">Please confirm your email</p>
                        </div>
                    </div>
                </div>

               <!-- Password Input -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                    <div>
                        <label for="password" class="block text-[20px] font-medium text-[#21272A] mb-2">Password</label>
                        <div class="mt-1 relative">
                            <input id="password"
                                name="password"
                                type="password"
                                required
                                class="appearance-none block w-full px-4 py-3 border border-[#E6E9F5] rounded-lg shadow-sm
                                        placeholder-gray-400 focus:outline-none focus:border-brand-main focus:ring-1
                                        focus:ring-brand-main !text-neutral-strong !bg-ipt-bg-1 !h-[48px] pr-10"
                                placeholder="*****">

                            <!-- Password Requirements Hint -->
                            <div class="mt-2 text-xs text-gray-600 hidden" id="password-requirements">
                                <p class="font-medium mb-1">Password must contain:</p>
                                <ul class="space-y-1">
                                    <li class="flex items-center" id="req-length">
                                        <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                            <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                            <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                        <span class="requirement-text">12 characters or more</span>
                                    </li>
                                    <li class="flex items-center" id="req-lowercase">
                                        <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                            <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                            <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                        <span class="requirement-text">At least 1 lowercase letter</span>
                                    </li>
                                    <li class="flex items-center" id="req-uppercase">
                                        <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                            <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                            <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                        <span class="requirement-text">At least 1 uppercase letter</span>
                                    </li>
                                    <li class="flex items-center" id="req-special">
                                        <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                            <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                            <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                        <span class="requirement-text">At least 1 special character</span>
                                    </li>
                                </ul>
                            </div>
                            <a class="absolute top-2 right-0 flex items-center pr-3 cursor-pointer" onclick="togglePassword('password', this)">
                                <svg class="h-5 w-5 text-gray-400 show-password" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                    <path d="M6.34315 7.34315C4.84285 8.84344 4 11 4 12C4 13 4.84285 15.1566 6.34315 16.6569C7.84344 18.1571 9.87827 19 12 19C14.1217 19 16.1566 18.1571 17.6569 16.6569C19.1571 15.1566 20 13 20 12C20 11 19.1571 8.84344 17.6569 7.34315C16.1566 5.84285 14.1217 5 12 5C9.87827 5 7.84344 5.84285 6.34315 7.34315Z" stroke="#21272A" stroke-width="2"/>
                                </svg>
                                <svg class="h-5 w-5 text-gray-400 hide-password hidden" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.99989 3L19.9999 21" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M14.8345 16.3775C13.9979 16.7803 13.0351 17 12 17C9.87827 17 7.84344 16.1571 6.34315 14.6569C4.84285 13.1566 4 11 4 10C4 9.29687 4.40519 8.10374 5.19371 7.00396" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M8.53113 6.04435C9.5888 5.36033 10.7843 5 12 5C14.1217 5 16.1566 5.84285 17.6569 7.34315C19.1571 8.84344 20 11 20 12C20 12.7031 19.5948 13.8963 18.8063 14.996" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                </svg>
                            </a>
                            <p id="password-error" class="mt-1 text-red-500 text-sm hidden">Please enter your password</p>
                        </div>
                    </div>
                    <div>
                        <label for="confirm_password" class="block text-[20px] font-medium text-[#21272A] mb-2">Confirm Password</label>
                        <div class="mt-1 relative">
                            <input id="confirm_password" 
                                name="confirm_password" 
                                type="password" 
                                required 
                                class="appearance-none block w-full px-4 py-3 border border-[#E6E9F5] rounded-lg shadow-sm 
                                        placeholder-gray-400 focus:outline-none focus:border-brand-main focus:ring-1 
                                        focus:ring-brand-main !text-neutral-strong !bg-ipt-bg-1 !h-[48px] pr-10"
                                placeholder="*****">
                            <a class="absolute top-2 right-0 flex items-center pr-3 cursor-pointer" onclick="togglePassword('confirm_password', this)">
                                <svg class="h-5 w-5 text-gray-400 show-password" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                    <path d="M6.34315 7.34315C4.84285 8.84344 4 11 4 12C4 13 4.84285 15.1566 6.34315 16.6569C7.84344 18.1571 9.87827 19 12 19C14.1217 19 16.1566 18.1571 17.6569 16.6569C19.1571 15.1566 20 13 20 12C20 11 19.1571 8.84344 17.6569 7.34315C16.1566 5.84285 14.1217 5 12 5C9.87827 5 7.84344 5.84285 6.34315 7.34315Z" stroke="#21272A" stroke-width="2"/>
                                </svg>
                                <svg class="h-5 w-5 text-gray-400 hide-password hidden" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.99989 3L19.9999 21" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M14.8345 16.3775C13.9979 16.7803 13.0351 17 12 17C9.87827 17 7.84344 16.1571 6.34315 14.6569C4.84285 13.1566 4 11 4 10C4 9.29687 4.40519 8.10374 5.19371 7.00396" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M8.53113 6.04435C9.5888 5.36033 10.7843 5 12 5C14.1217 5 16.1566 5.84285 17.6569 7.34315C19.1571 8.84344 20 11 20 12C20 12.7031 19.5948 13.8963 18.8063 14.996" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                </svg>
                            </a>
                            <p id="confirm-password-error" class="mt-1 text-red-500 text-sm hidden">Please confirm your password</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-end">
                    <!-- <div class="text-sm">
                        <a href="#" class="font-medium text-16 text-neutral-strong hover:text-neutral-strong">Forgot password?</a>
                    </div> -->
                </div>

                <div>
                    <button type="button" id="registerFormBtn" class="w-full mx-auto flex !text-primary-main hover:bg-brand-main/80 justify-center py-2 px-4 rounded-md shadow-sm text-16 font-medium text-white bg-brand-main hover:bg-brand-main focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                        Register
                    </button>
                </div>

                <div>
                    <button type="button" onclick="window.location.href='<?php echo get_permalink(41); ?>'" class="w-full hover:text-white mx-auto flex justify-center py-2 px-4 rounded-md shadow-sm text-16 font-medium text-white bg-neutral-strong hover:bg-neutral-strong/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                        Already have an account? <span class="underline pl-1">Login</span>
                    </button>
                    <div class="mt-6 text-center text-16 text-neutral-strong font-semibold">
                        By continuing, you agree to our 
                        <a href="<?php echo get_permalink(79); ?>" class="font-semibold text-neutral-strong hover:text-brand-main active:text-neutral-strong focus:text-neutral-strong underline hover:underline">Privacy Policy</a>
                        and
                        <a href="<?php echo get_permalink(81); ?>" class="font-semibold text-neutral-strong hover:text-brand-main active:text-neutral-strong focus:text-neutral-strong underline hover:underline">Terms of Service</a>
                    </div>
                </div>
                </form>
                <script>
                    jQuery(document).ready(function($) {

                        // Password requirements validation
                        $('#password').on('input', function() {
                            const password = $(this).val();
                            const requirementsDiv = $('#password-requirements');

                            // Show/hide requirements based on password field content
                            if (password.length > 0) {
                                requirementsDiv.removeClass('hidden');

                                // Check length (12+ characters)
                                const hasLength = password.length >= 12;
                                updateRequirement('req-length', hasLength);

                                // Check lowercase
                                const hasLowercase = /[a-z]/.test(password);
                                updateRequirement('req-lowercase', hasLowercase);

                                // Check uppercase
                                const hasUppercase = /[A-Z]/.test(password);
                                updateRequirement('req-uppercase', hasUppercase);

                                // Check special character
                                const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
                                updateRequirement('req-special', hasSpecial);
                            } else {
                                requirementsDiv.addClass('hidden');
                            }
                        });

                        function updateRequirement(reqId, isValid) {
                            const reqElement = $('#' + reqId);
                            const dot = reqElement.find('.requirement-dot');
                            const check = reqElement.find('.requirement-check');
                            const text = reqElement.find('.requirement-text');

                            if (isValid) {
                                dot.addClass('hidden');
                                check.removeClass('hidden');
                                text.addClass('text-green-600');
                                text.removeClass('text-gray-600');
                            } else {
                                dot.removeClass('hidden');
                                check.addClass('hidden');
                                text.removeClass('text-green-600');
                                text.addClass('text-gray-600');
                            }
                        }

                        // Hàm hiển thị lỗi
                        function showError(fieldId, show, message = null) {
                            const errorElement = document.getElementById(fieldId + '-error');
                            if (errorElement) {
                                if (show) {
                                    if (message) {
                                        errorElement.textContent = message;
                                    }
                                    errorElement.classList.remove('hidden');
                                } else {
                                    errorElement.classList.add('hidden');
                                    // Khôi phục lại thông báo mặc định
                                    if (fieldId === 'email') {
                                        errorElement.textContent = 'Please enter your email';
                                    } else if (fieldId === 'confirm-email') {
                                        errorElement.textContent = 'Please confirm your email';
                                    } else if (fieldId === 'password') {
                                        errorElement.textContent = 'Please enter your password';
                                    } else if (fieldId === 'confirm-password') {
                                        errorElement.textContent = 'Please confirm your password';
                                    }
                                }
                            }
                        }
                        
                        // Hàm kiểm tra email hợp lệ
                        function isValidEmail(email) {
                            const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
                            return emailRegex.test(email);
                        }
                        
                        // Xử lý khi input thay đổi để ẩn thông báo lỗi
                        $('#email, #confirm_email, #password, #confirm_password').on('input', function() {
                            const fieldId = this.id;
                            if (this.value.trim() !== '') {
                                showError(fieldId === 'confirm_email' ? 'confirm-email' : 
                                          fieldId === 'confirm_password' ? 'confirm-password' : fieldId, false);
                            }
                            
                            // Kiểm tra email và confirm_email khi thay đổi
                            if (fieldId === 'email' || fieldId === 'confirm_email') {
                                const email = $('#email').val().trim();
                                const confirmEmail = $('#confirm_email').val().trim();
                                
                                if (confirmEmail !== '' && email !== '' && email !== confirmEmail) {
                                    showError('confirm-email', true, 'Email addresses do not match');
                                } else if (confirmEmail !== '' && email !== '') {
                                    showError('confirm-email', false);
                                }
                            }
                            
                            // Kiểm tra password và confirm_password khi thay đổi
                            if (fieldId === 'password' || fieldId === 'confirm_password') {
                                const password = $('#password').val().trim();
                                const confirmPassword = $('#confirm_password').val().trim();
                                
                                if (confirmPassword !== '' && password !== '' && password !== confirmPassword) {
                                    showError('confirm-password', true, 'Passwords do not match');
                                } else if (confirmPassword !== '' && password !== '') {
                                    showError('confirm-password', false);
                                }
                            }
                        });
                        
                        $('#registerFormBtn').on('click', function(e) {
                            // Lấy dữ liệu từ form
                            const email = document.getElementById('email').value.trim();
                            const confirmEmail = document.getElementById('confirm_email').value.trim();
                            const password = document.getElementById('password').value.trim();
                            const confirm_password = document.getElementById('confirm_password').value.trim();
                            
                            // Kiểm tra và hiển thị lỗi
                            let hasError = false;
                            
                            if (email === '') {
                                showError('email', true);
                                hasError = true;
                            } else if (!isValidEmail(email)) {
                                showError('email', true, 'Please enter a valid email address');
                                hasError = true;
                            } else {
                                showError('email', false);
                            }
                            
                            if (confirmEmail === '') {
                                showError('confirm-email', true);
                                hasError = true;
                            } else if (confirmEmail !== email) {
                                showError('confirm-email', true, 'Email addresses do not match');
                                hasError = true;
                            } else {
                                showError('confirm-email', false);
                            }
                            
                            if (password === '') {
                                showError('password', true);
                                hasError = true;
                            } else {
                                showError('password', false);
                            }
                            
                            if (confirm_password === '') {
                                showError('confirm-password', true);
                                hasError = true;
                            } else if (confirm_password !== password) {
                                showError('confirm-password', true, 'Passwords do not match');
                                hasError = true;
                            } else {
                                showError('confirm-password', false);
                            }
                            
                            if (hasError) {
                                return;
                            }

                            /* Industry list */
                            const queryRegister = `
                                mutation Auth_register($body: RegisterInputDto!) {
                                    auth_register(
                                        body: $body
                                    )
                                }
                            `;

                            const variables = {
                                body: {
                                    first_name: email,
                                    last_name: email,
                                    email: email,
                                    password: password,
                                    confirm_password: confirm_password,
                                    role_id: 2
                                }
                            };

                            // Hiển thị trạng thái đang xử lý
                            const originalBtnText = $(this).text();
                            $(this).text('Processing...').prop('disabled', true);

                            $.ajax({
                                url: iptHomeAjax.ajax_url,
                                type: 'POST',
                                dataType: 'json',
                                data: {
                                    action: 'ipt_home_graphql',
                                    query: queryRegister,
                                    variables: JSON.stringify(variables)   
                                },
                                success: function(response) {
                                    // Khôi phục trạng thái nút
                                    $('#registerFormBtn').text(originalBtnText).prop('disabled', false);
                                    
                                    // Check if GraphQL registration was successful
                                    if (response.data && response.data.auth_register) {
                                        // Create WordPress user
                                        $.ajax({
                                            url: iptHomeAjax.ajax_url,
                                            type: 'POST',
                                            dataType: 'json',
                                            data: {
                                                action: 'create_wp_user',
                                                email: email,
                                                password: password
                                            },
                                            success: function(wpResponse) {
                                                if (wpResponse.success) {
                                                    // Sử dụng hàm showToast đơn giản
                                                    showSuccessToast('Registration successful!');
                                                    
                                                    // Redirect to login page or dashboard sau 1 giây
                                                    setTimeout(function() {
                                                        window.location.href = '<?php echo home_url(); ?>?page_id=41&registered=true';
                                                    }, 1000);
                                                } else {
                                                    // Sử dụng hàm showToast đơn giản
                                                    showErrorToast(wpResponse.data.message || 'Unable to create account');
                                                }
                                            },
                                            error: function(xhr, status, error) {
                                                // Sử dụng hàm showToast đơn giản
                                                showErrorToast('Error creating account');
                                                console.error('Error creating WordPress user:', error);
                                            }
                                        });
                                    } else if (response.errors) {
                                        // Xử lý lỗi từ API
                                        console.log('Registration failed:', response.errors);
                                        
                                        // Kiểm tra cấu trúc lỗi
                                        if (response.errors && response.errors.length > 0) {
                                            const firstError = response.errors[0];
                                            
                                            // Kiểm tra nếu có lỗi validation
                                            if (firstError.errors && firstError.errors.length > 0) {
                                                // Xử lý từng lỗi validation
                                                firstError.errors.forEach(function(validationError) {
                                                    const property = validationError.property;
                                                    let errorMessage = '';
                                                    
                                                    // Lấy thông báo lỗi từ constraints
                                                    if (validationError.constraints) {
                                                        const constraintKeys = Object.keys(validationError.constraints);
                                                        if (constraintKeys.length > 0) {
                                                            errorMessage = validationError.constraints[constraintKeys[0]];
                                                        }
                                                    }
                                                    
                                                    // Hiển thị lỗi tại vị trí tương ứng
                                                    if (property === 'email') {
                                                        showError('email', true, errorMessage);
                                                    } else if (property === 'password') {
                                                        showError('password', true, errorMessage);
                                                    } else if (property === 'confirm_password') {
                                                        showError('confirm-password', true, errorMessage);
                                                    } else {
                                                        // Hiển thị lỗi chung nếu không xác định được vị trí
                                                        showErrorToast(errorMessage || firstError.message || 'Registration failed');
                                                    }
                                                });
                                            } else if (firstError.message) {
                                                // Hiển thị lỗi chung nếu không có lỗi validation cụ thể
                                                showErrorToast(firstError.message);
                                            }
                                        } else {
                                            // Hiển thị lỗi chung nếu không có cấu trúc lỗi cụ thể
                                            showErrorToast('Registration failed. Please try again.');
                                        }
                                    } else {
                                        // Hiển thị lỗi chung nếu không có thông tin lỗi
                                        showErrorToast('Registration failed. Please try again.');
                                    }
                                },
                                error: function(xhr, status, error) {
                                    // Khôi phục trạng thái nút
                                    $('#registerFormBtn').text(originalBtnText).prop('disabled', false);
                                    
                                    console.error('AJAX error:', error);
                                    
                                    // Thử phân tích response để tìm lỗi
                                    try {
                                        const response = JSON.parse(xhr.responseText);
                                        if (response.errors && response.errors.length > 0) {
                                            // Xử lý lỗi tương tự như trong success callback
                                            const firstError = response.errors[0];
                                            
                                            if (firstError.errors && firstError.errors.length > 0) {
                                                firstError.errors.forEach(function(validationError) {
                                                    const property = validationError.property;
                                                    let errorMessage = '';
                                                    
                                                    if (validationError.constraints) {
                                                        const constraintKeys = Object.keys(validationError.constraints);
                                                        if (constraintKeys.length > 0) {
                                                            errorMessage = validationError.constraints[constraintKeys[0]];
                                                        }
                                                    }
                                                    
                                                    if (property === 'email') {
                                                        showError('email', true, errorMessage);
                                                    } else if (property === 'password') {
                                                        showError('password', true, errorMessage);
                                                    } else if (property === 'confirm_password') {
                                                        showError('confirm-password', true, errorMessage);
                                                    } else {
                                                        // Hiển thị lỗi chung
                                                        showErrorToast(errorMessage || 'Data validation error');
                                                    }
                                                });
                                            } else if (firstError.message) {
                                                // Hiển thị lỗi chung
                                                showErrorToast(firstError.message);
                                            }
                                        } else {
                                            // Hiển thị lỗi chung
                                            showErrorToast('Registration failed. Please try again.');
                                        }
                                    } catch (e) {
                                        console.error('Error parsing response:', e);
                                        showErrorToast('Registration failed. Please try again.');
                                    }
                                }
                            });
                        });
                    });
                </script>    
                </script>

                <div class="mt-6">
                   

                    <div class="mt-6 relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full md:w-[412px] mx-auto border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or</span>
                        </div>
                    </div>

                    <div class="mt-6 flex flex-col gap-[16px]">
                        <button type="button" onclick="handleGoogleRegister()" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google logo">
                                    <span>Sign up with Google</span>
                                </div>
                            </div>
                        </button>
                        <button type="button" onclick="handleFacebookRegister()" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/fb-ico.png" alt="Facebook logo">
                                    <span>Sign up with Facebook</span>
                                </div>
                            </div>
                        </button>
                        <button type="button" onclick='window.location.href="<?php echo get_permalink(41); ?>"' class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/email-ico.png" alt="Email logo">
                                    <span>Continue with Email</span>
                                </div>
                            </div>
                        </button>
                        <!-- <button type="button" onclick="handleAppleRegister()" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/apple-ico.png" alt="Apple logo">
                                    <span>Sign up with Apple</span>
                                </div>
                            </div>
                        </button> -->
                        <!-- <button type="button" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center justify-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <img class="h-5 w-5 mr-2" src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google logo">
                            Sign in with Google
                        </button>
                        <button type="button" class="w-full md:w-[412px] mx-auto h-[56px]  flex items-center justify-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/fb-ico.png" alt="Google logo">
                            Sign in with Facebook
                        </button>
                        <button type="button" class="w-full md:w-[412px] mx-auto h-[56px]  flex items-center justify-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/apple-ico.png" alt="Google logo">
                            Sign in with Apple
                        </button> -->
                        <!-- <a href="#" type="button" class="w-full h-[56px] hover:text-brand-main/80 flex items-center justify-center py-[16px] px-[24px] border-none text-16 font-semibold text-brand-main bg-transparent shadow-none focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            Continue with SSO
                        </a> -->
                        <!-- <div class="sso-widget-container items-center justify-center !outline-none ">      
                            <div id="saml_login_widget-2" class="widget widget_saml_login_widget sso-widget">			
                                <script>
                                    function submitSamlForm() {
                                        document.getElementById("miniorange-saml-sp-sso-login-form").submit();
                                    }
                                </script>
                                <form name="miniorange-saml-sp-sso-login-form" id="miniorange-saml-sp-sso-login-form" method="post" action="">
                                    <input type="hidden" name="option" value="saml_user_login">
                                    <font size="+1" style="vertical-align:top;"> </font>
                                    <a href="javascript:void(0);" onclick="submitSamlForm()" class="cursor-pointer w-full h-[56px] hover:text-brand-main/80 flex items-center justify-center py-[16px] px-[24px] !border-none text-16 font-semibold !text-brand-main hover:!text-brand-main bg-transparent shadow-none !outline-none focus:!outline-none active:!outline-none hover:!outline-none">Continue with SSO</a>
                                </form>
                            </div>
                        </div> -->
                    </div>
                </div>
            
        </div>
    </div>
    <!-- Firebase auth handlers are loaded from external JS file: /assets/js/firebase-auth-handlers.js -->
    <!-- /. Page content -->
</main><!-- #page -->

<?php
get_footer(); 
